package com.lhx.birthday.service;

import com.lhx.birthday.entity.CdKey;
import com.lhx.birthday.entity.ProfilePrompt;
import com.lhx.birthday.enums.ActionUnit;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.request.userinfo.TouristsLoginRequest;
import com.lhx.birthday.request.userinfo.UserLoginRequest;
import com.lhx.birthday.request.userinfo.UserLoginWithAppleRequest;
import com.lhx.birthday.request.userinfo.UserUpdateRequest;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/27 09:45
 */
public interface IUserInfoService {

    UserInfoVO getByUserId(Long userId);

    UserInfoVO getByPhone(String phone);

    int updateUserInfo(Long userId,UserUpdateRequest userUpdateRequest);

    int updateVipExpireDate(Long userId, LocalDateTime vipExpireDate, String productId,Integer vip);

    int updateFreeUse(Long userId);

    int updateRewardVipBeginDate(Long userId, LocalDateTime rewardVipBeginDate);

    UserInfoVO login(UserLoginRequest userLoginRequest,UserInfoVO beforeUserInfoVO);

    UserInfoVO loginWithApple(UserLoginWithAppleRequest userLoginWithAppleRequest, UserInfoVO beforeUserInfoVO);

    UserInfoVO touristsLogin(TouristsLoginRequest touristsLoginRequest);

    boolean cancelUser(Long userId);

    CdKey exchange(String phone, Integer cdkType, LocalDate date);

    Result<Boolean> expire(String phone);

    void initPushData(List<ProfilePrompt> profilePrompts, ActionUnit actionUnit,Integer day);

    /**
     * 更新挽回日期
     * @param userId 用户ID
     * @param recoverDate 挽回日期
     */
    void updateRecoverDate(Long userId, LocalDate recoverDate);

    /**
     * 更新挽回状态
     * @param userId 用户ID
     * @param recoverState 挽回状态
     */
    void updateRecoverState(Long userId, Integer recoverState);
    
    /**
     * 更新用户的星座推送设置到Redis
     * @param userId 用户ID
     * @param zodiacSignType 星座类型
     * @param timeValue 提醒时间
     * @param actionUnit 操作类型（添加或删除）
     */
    void updateZodiacPushSettings(Long userId, ZodiacSignType zodiacSignType, String timeValue, ActionUnit actionUnit);
    
    /**
     * 更新用户的推送设置到Redis
     * @param userId 用户ID
     * @param settingType 设置类型，如 "retrograde:warning", "planet:change" 等
     * @param timeValue 时间值，仅星座提醒等需要具体时间的设置使用
     * @param actionUnit 操作类型（添加或删除）
     */
    void updatePushSettings(Long userId, String settingType, String timeValue, ActionUnit actionUnit);
}
