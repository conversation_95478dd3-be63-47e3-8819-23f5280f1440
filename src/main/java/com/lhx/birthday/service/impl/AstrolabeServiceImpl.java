package com.lhx.birthday.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap;
import com.lhx.birthday.constant.ApiConstant;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.constant.SystemConfigConstant;
import com.lhx.birthday.entity.AstroCalendarEvent;
import com.lhx.birthday.entity.ConstellationProfile;
import com.lhx.birthday.entity.CorpusAstro;
import com.lhx.birthday.entity.CorpusAstroEn;
import com.lhx.birthday.entity.CountryCode;
import com.lhx.birthday.entity.SystemConfig;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.mapper.AstroCalendarEventMapper;
import com.lhx.birthday.mapper.ConstellationMapper;
import com.lhx.birthday.mapper.CorpusAstroMapper;
import com.lhx.birthday.mapper.CorpusAstroEnMapper;
import com.lhx.birthday.mapper.CountryCodeMapper;
import com.lhx.birthday.mapper.SystemConfigMapper;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.request.astrolabe.*;
import com.lhx.birthday.service.IAstrolabeService;
import com.lhx.birthday.util.converUtil.ConverChinese;
import com.lhx.birthday.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import org.springframework.data.jpa.domain.Specification;
import javax.persistence.criteria.Predicate;
import com.lhx.birthday.util.HttpClientUtil;

@Slf4j
@Service
public class AstrolabeServiceImpl implements IAstrolabeService {

    private static final DateTimeFormatter FLEXIBLE_DATETIME_FORMATTER = new DateTimeFormatterBuilder()
            .appendPattern("yyyy-MM-dd HH:mm")
            .optionalStart()
            .appendPattern(":ss")
            .optionalEnd()
            .toFormatter();

    @Value("${xingpan.access_token}")
    private String token;

    @Value("${xingpan.url}")
    private String url;

    @Value("${deepseek.api.key:***********************************}")
    private String deepseekApiKey;
    
    @Value("${deepseek.api.url:https://api.deepseek.com/v1/chat/completions}")
    private String deepseekApiUrl;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ConstellationMapper constellationMapper;

    @Autowired
    private CountryCodeMapper countryCodeMapper;

    @Autowired
    private AstroCalendarEventMapper astroCalendarEventMapper;
    
    @Autowired
    private SystemConfigMapper systemConfigMapper;
    
    @Autowired
    private CorpusAstroMapper corpusAstroMapper;

    @Autowired
    private CorpusAstroEnMapper corpusAstroEnMapper;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 新闻内容
     * @param articleDetailsRequest
     * @return
     */
    @Override
    public Result articleDetails(ArticleDetailsRequest articleDetailsRequest) {
        String requestData = "access_token=" + token + "&name=" + articleDetailsRequest.getName();
        HttpResponse response = sendPostRequest(url+ApiConstant.API_XINGPAN_ARTICLE, requestData);
        String result = response.body();
        response.close();
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject.getInteger("code").equals(0)) {
            JSONObject dataObj = jsonObject.getJSONObject("data");
            return Result.ok(dataObj);
        }else{
            return Result.error(jsonObject.getInteger("code"),jsonObject.getString("msg"));
        }
    }

    @Override
    public Result chartNatal(NatalRequest natalRequest) {
        try {
            // 获取星座档案ID
            Long constellationId = natalRequest.getConstellationId();
            if (constellationId == null) {
                return Result.error("星座档案ID不能为空");
            }
            
            // 构建Redis缓存键，基于请求的所有参数（包含语言类型）
            // 将请求对象转换为JSON字符串，用于生成唯一的缓存键
            String requestJson = JSONObject.toJSONString(natalRequest);
            String requestHash = String.valueOf(requestJson.hashCode());
            String languageType = natalRequest.getLanguageType() != null ? natalRequest.getLanguageType().name() : "ZH_CN";
            String redisKey = RedisKeyConstant.XINGPAN_CHART_DATA_KEY + languageType + ":" + constellationId + ":" + requestHash;
            
            // 如果不需要重新获取数据，尝试从Redis获取数据
            if (!"1".equals(natalRequest.getIsCorpus())) {
                String chartDataJson = redisService.getString(redisKey);
                if (StringUtils.isNotEmpty(chartDataJson)) {
                    JSONObject chartData = JSONObject.parseObject(chartDataJson);
                    // 统一替换 planet_english 特定取值
                    replacePlanetEnglishInJson(chartData);
                    return Result.ok(chartData);
                }
            }
            
            // 获取星座档案信息
            Optional<ConstellationProfile> constellationProfileOptional = constellationMapper.findById(natalRequest.getConstellationId());
            if (!constellationProfileOptional.isPresent()) {
                return Result.error("星座档案不存在");
            }
            ConstellationProfile constellationProfile = constellationProfileOptional.get();

            // 获取出生地点信息
            CountryCode birthPlace = countryCodeMapper.findById(Long.parseLong(constellationProfile.getBirthPlaceId())).orElse(null);
            if (birthPlace == null) {
                return Result.error("出生地点信息不存在");
            }

            // 构建请求参数
            cn.hutool.json.JSONObject parseObj = new cn.hutool.json.JSONObject();
            parseObj.putOpt("access_token", token);
            parseObj.putOpt("planets", natalRequest.getPlanets());
            parseObj.putOpt(natalRequest.getPlanetXsName(), natalRequest.getPlanetXs());
            parseObj.putOpt("virtual", natalRequest.getVirtual());
            parseObj.putOpt(natalRequest.getHSysName(), natalRequest.getHSys());
            parseObj.putOpt("longitude", birthPlace.getLon());
            parseObj.putOpt("latitude", birthPlace.getLat());
            parseObj.putOpt("tz", constellationProfile.getTimeZone());
            parseObj.putOpt("birthday", constellationProfile.getBirthdayTime());
            parseObj.putOpt(natalRequest.getSvgTypeName(), natalRequest.getSvgType());
            
            // 处理phase参数，将Map转换为API所需的格式
            if (natalRequest.getPhase() != null && !natalRequest.getPhase().isEmpty()) {
                for (Map.Entry<Integer, Float> entry : natalRequest.getPhase().entrySet()) {
                    parseObj.putOpt("phase[" + entry.getKey() + "]", entry.getValue());
                }
            }
            
            parseObj.putOpt(natalRequest.getIsCorpusName(), natalRequest.getIsCorpus());

            HttpResponse response = sendJsonPostRequest(url + ApiConstant.API_XINGPAN_CHART_NATAL, parseObj.toString());
            String result = response.body();
            response.close();
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.getInteger("code").equals(0)) {
                JSONObject data = jsonObject.getJSONObject("data");
                JSONArray planets = data.getJSONArray("planet");
                for (int i = 0; i < planets.size(); i++) {
                    JSONObject plant = planets.getJSONObject(i);
                    Integer signId = plant.getJSONObject("sign").getInteger("sign_id");
                    Integer houseId = plant.getInteger("house_id");
                    String plantId = plant.getString("code_name");
                    // 根据语言类型选择不同的Redis键
                    String constellationRedisKey = getConstellationRedisKey(natalRequest.getLanguageType());
                    String zodiacRedisKey = getZodiacRedisKey(natalRequest.getLanguageType());
                    String allowRedisKey = getAllowRedisKey(natalRequest.getLanguageType());

                    // 星座
                    String constellationJson = redisService.getString(constellationRedisKey + plantId + ":" + signId);
                    String zodiadcJson = redisService.getString(zodiacRedisKey + plantId + ":" + houseId);
                    JSONArray corpusArr = new JSONArray();
                    if (StringUtils.isNotEmpty(constellationJson)) {
                        corpusArr.add(JSONObject.parseObject(constellationJson));
                    }
                    if (StringUtils.isNotEmpty(zodiadcJson)) {
                        corpusArr.add(JSONObject.parseObject(zodiadcJson));
                    }
                    plant.put("corpus", corpusArr);
                    // 角度
                    JSONArray planetAllowDegree = plant.getJSONArray("planet_allow_degree");
                    for (int j = 0; j < planetAllowDegree.size(); j++) {
                        JSONObject allowDegreeObj = planetAllowDegree.getJSONObject(j);
                        String allowJson = redisService.getString(allowRedisKey + plantId + ":" + allowDegreeObj.getString("code_name") + ":" + allowDegreeObj.get("allow"));
                        allowDegreeObj.put("corpus", JSONObject.parseObject(allowJson));
                    }
                }
                
                // 将数据存入Redis，设置过期时间为8小时
                // 缓存与返回前进行统一替换 planet_english 特定取值
                replacePlanetEnglishInJson(data);
                redisService.setString(redisKey, data.toJSONString(), 8 * 3600);
                
                return Result.ok(data);
            } else {
                return Result.error(jsonObject.getInteger("code"), jsonObject.getString("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("系统异常");
        }
    }

    /**
     * 递归替换 JSON 结构中所有 key 为 "planet_english" 的值：
     * meanNode -> North Node, meanSouthNode -> South Node, meanApogee -> Lilith
     *
     * @param node JSON 对象或数组
     */
    private void replacePlanetEnglishInJson(Object node) {
        if (node instanceof JSONObject) {
            JSONObject object = (JSONObject) node;
            for (String key : new ArrayList<>(object.keySet())) {
                Object value = object.get(key);
                if ("planet_english".equals(key) && value instanceof String) {
                    String english = (String) value;
                    if ("meanNode".equals(english)) {
                        object.put(key, "North Node");
                    } else if ("meanSouthNode".equals(english)) {
                        object.put(key, "South Node");
                    } else if ("meanApogee".equals(english)) {
                        object.put(key, "Lilith");
                    }
                }
                if (value instanceof JSONObject || value instanceof JSONArray) {
                    replacePlanetEnglishInJson(value);
                }
            }
        } else if (node instanceof JSONArray) {
            JSONArray array = (JSONArray) node;
            for (int i = 0; i < array.size(); i++) {
                Object item = array.get(i);
                if (item instanceof JSONObject || item instanceof JSONArray) {
                    replacePlanetEnglishInJson(item);
                }
            }
        }
    }

    @Override
    public Result report(YearReportRequest yearReportRequest) {
        String requestData = "access_token=" + token +
                "&name=" + yearReportRequest.getName() +
                "&sex=" + yearReportRequest.getSex() +
                "&birthday=" + yearReportRequest.getBirthday() +
                "&birth_longitude=" + yearReportRequest.getBirthLongitude() +
                "&birth_latitude=" + yearReportRequest.getBirthLatitude() +
                "&birth_tz=" + yearReportRequest.getBirthTz() +
                "&living_longitude=" + yearReportRequest.getLivingLongitude() +
                "&living_latitude=" + yearReportRequest.getLivingLatitude() +
                "&living_tz=" + yearReportRequest.getLivingTz();
        HttpResponse response = sendPostRequest(ApiConstant.API_XINGPAN_YEAR_REPORT, requestData);
        String result = response.body();
        response.close();
        JSONObject jsonObject;
        if(yearReportRequest.getLanguageType().equals(LanguageType.ZH_HANT)){
            jsonObject = ConverChinese.convertValuesToTraditionalChinese(result);
        }else{
            jsonObject = JSONObject.parseObject(result);
        }
        if (jsonObject.getInteger("code").equals(0)) {
            return Result.ok(jsonObject.getJSONObject("data"));
        }else{
            return Result.error(jsonObject.getInteger("code"),jsonObject.getString("msg"));
        }
    }

    @Override
    public Result corpusconstellationList(CorpusconstellationRequest corpusconstellationRequest) {
        String requestData = "access_token=" + token +
                "&fallInto=" + corpusconstellationRequest.getFallInto() +
                "&chartType=" + corpusconstellationRequest.getChartType();
        HttpResponse response = sendPostRequest(url+ApiConstant.API_XINGPAN_CORPUSCONSTELLATION_LIST, requestData);
        String result = response.body();
        response.close();
        JSONObject jsonObject;
        if(corpusconstellationRequest.getLanguageType().equals(LanguageType.ZH_HANT)){
            jsonObject = ConverChinese.convertValuesToTraditionalChinese(result);
        }else{
            jsonObject = JSONObject.parseObject(result);
        }
        if (jsonObject.getInteger("code").equals(0)) {
            return Result.ok(jsonObject.getJSONArray("data"));
        }else{
            return Result.error(jsonObject.getInteger("code"),jsonObject.getString("msg"));
        }
    }

    @Override
    public Result evaluationcombination(EvaluationcombinationRequest evaluationcombinationRequest) {
        try {
            // 验证参数
            List<Long> constellationIds = evaluationcombinationRequest.getConstellationIds();
            if (constellationIds == null || constellationIds.size() != 2) {
                return Result.error("星座档案ID必须提供两个");
            }
            
            // 创建Redis缓存键（包含语言类型）
            String languageType = evaluationcombinationRequest.getLanguageType() != null ? evaluationcombinationRequest.getLanguageType().name() : "ZH_CN";
            String requestHash = String.valueOf(JSONObject.toJSONString(evaluationcombinationRequest).hashCode());
            String redisKey = RedisKeyConstant.XINGPAN_CHART_DATA_KEY + languageType + ":combination:" + requestHash;
            
            // 尝试从Redis获取数据
            String cachedData = redisService.getString(redisKey);
            if (StringUtils.isNotEmpty(cachedData)) {
                JSONObject data = JSONObject.parseObject(cachedData);
                return Result.ok(data);
            }
            
            // 获取两个档案的信息
            List<ConstellationProfile> profiles = new ArrayList<>();
            for (Long constellationId : constellationIds) {
                Optional<ConstellationProfile> profileOptional = constellationMapper.findById(constellationId);
                if (!profileOptional.isPresent()) {
                    return Result.error("星座档案不存在: " + constellationId);
                }
                profiles.add(profileOptional.get());
            }
            
            // 构建用户列表数据
            JSONArray userList = new JSONArray();
            for (ConstellationProfile profile : profiles) {
                // 获取出生地点信息
                CountryCode birthPlace = countryCodeMapper.findById(Long.parseLong(profile.getBirthPlaceId())).orElse(null);
                if (birthPlace == null) {
                    return Result.error("出生地点信息不存在");
                }
                
                // 创建用户信息对象
                JSONObject userInfo = new JSONObject();
                userInfo.put("birthday", profile.getBirthdayTime());
                userInfo.put("longitude", birthPlace.getLon());
                userInfo.put("latitude", birthPlace.getLat());
                userInfo.put("tz", profile.getTimeZone());
                userInfo.put("nickname", profile.getName());
                
                userList.add(userInfo);
            }
            
            // 构建请求参数并发送请求
            String requestData = "access_token=" + token +
                    "&user_list=" + userList.toJSONString() +
                    "&type=" + evaluationcombinationRequest.getType();
            
            HttpResponse response = sendPostRequest(url+ApiConstant.API_XINGPAN_EVALUATIONCOMBINATION, requestData);
            String result = response.body();
            response.close();
            JSONObject jsonObject = JSONObject.parseObject(result);
            
            if (jsonObject.getInteger("code").equals(0)) {
                JSONObject data = jsonObject.getJSONObject("data");
                
                // 将数据存入Redis缓存，有效期8小时
                redisService.setString(redisKey, data.toJSONString(), 8 * 3600);
                
                return Result.ok(data);
            } else {
                return Result.error(jsonObject.getInteger("code"), jsonObject.getString("msg"));
            }
        } catch (Exception e) {
            log.error("缘分合盘异常", e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result luck(LuckRequest luckRequest) {
        String requestData = "access_token=" + token +
                "&longitude=" + luckRequest.getLongitude() +
                "&latitude=" + luckRequest.getLatitude() +
                "&tz=" + luckRequest.getTz() +
                "&birthday=" + luckRequest.getBirthday();
        String api;
        if(luckRequest.getType()==0){
            api = ApiConstant.API_XINGPAN_LUCK_DAY;
        }else if(luckRequest.getType()==1){
            api = ApiConstant.API_XINGPAN_LUCK_WEEK;
        }else if(luckRequest.getType()==2){
            api = ApiConstant.API_XINGPAN_LUCK_MOON;
        }else {
            api = ApiConstant.API_XINGPAN_LUCK_YEAR;
        }
        HttpResponse response = sendPostRequest(url+api, requestData);
        String result = response.body();
        response.close();
        JSONObject jsonObject;
        if(luckRequest.getLanguageType().equals(LanguageType.ZH_HANT)){
            jsonObject = ConverChinese.convertValuesToTraditionalChinese(result);
        }else{
            jsonObject = JSONObject.parseObject(result);
        }
        if (jsonObject.getInteger("code").equals(0)) {
            JSONArray data = jsonObject.getJSONArray("data");
            return Result.ok(data);
        }else{
            return Result.error(jsonObject.getInteger("code"),jsonObject.getString("msg"));
        }
    }

    @Override
    public JSONObject getNatalChart(Long constellationId) {
        Optional<ConstellationProfile> profileOptional = constellationMapper.findById(constellationId);
        if (!profileOptional.isPresent()) {
            throw new RuntimeException("星盘档案不存在");
        }
        
        ConstellationProfile profile = profileOptional.get();
        
        // 创建NatalRequest对象
        NatalRequest natalRequest = new NatalRequest();
        natalRequest.setConstellationId(constellationId);
        natalRequest.setPlanets(Arrays.asList(0,1,2,3,4,5,6,7,8,9,"m","H"));
        natalRequest.setPlanetXs(new ArrayList<>());
        natalRequest.setVirtual(Arrays.asList(10));
        natalRequest.setHSys("p");
        natalRequest.setIsCorpus("1"); // 需要语料数据
        
        // 创建默认的phase映射（可以设为null或空Map，取决于业务需求）
        Map<Integer, Float> defaultPhaseMap = new HashMap<>();
        defaultPhaseMap.put(0, 0f);
        defaultPhaseMap.put(30, 2f);
        defaultPhaseMap.put(36, 2f);
        defaultPhaseMap.put(45, 2f);
        defaultPhaseMap.put(60, 6f);
        defaultPhaseMap.put(72, 2f);
        defaultPhaseMap.put(90, 6f);
        defaultPhaseMap.put(120, 6f);
        defaultPhaseMap.put(135, 0.5f);
        defaultPhaseMap.put(144, 2f);
        defaultPhaseMap.put(150, 2f);
        defaultPhaseMap.put(180, 6f);
        natalRequest.setPhase(defaultPhaseMap);

        // 调用本命盘接口
        Result chartResult = chartNatal(natalRequest);
        if (!chartResult.isSuccess()) {
            throw new RuntimeException("获取星盘数据失败: " + chartResult.getMessage());
        }
        
        // 获取结果
        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(chartResult.getResult()));
        
        // 解析结果
        JSONArray planets = result.getJSONArray("planet");
        String sunSign = null;
        String moonSign = null;
        String ascendantSign = null;

        for (int i = 0; i < planets.size(); i++) {
            JSONObject planet = planets.getJSONObject(i);
            String planetChinese = planet.getString("planet_chinese");
            JSONObject signObj = planet.getJSONObject("sign");
            
            if ("太阳".equals(planetChinese)) {
                sunSign = signObj.getString("sign_chinese");
            } else if ("月亮".equals(planetChinese)) {
                moonSign = signObj.getString("sign_chinese");
            } else if ("上升".equals(planetChinese)) {
                ascendantSign = signObj.getString("sign_chinese");
            }
        }

        // 更新档案信息
        profile.setSunSign(sunSign);
        profile.setMoonSign(moonSign);
        profile.setAscendantSign(ascendantSign);
        constellationMapper.save(profile);

        return result;
    }

    public Result chartTransit(TransitRequest transitRequest) {
        try {
            // 获取星座档案ID
            Long constellationId = transitRequest.getConstellationId();
            if (constellationId == null) {
                return Result.error("星座档案ID不能为空");
            }
            
            // 构建Redis缓存键，基于请求的所有参数（包含语言类型）
            String requestJson = JSONObject.toJSONString(transitRequest);
            String requestHash = String.valueOf(requestJson.hashCode());
            String languageType = transitRequest.getLanguageType() != null ? transitRequest.getLanguageType().name() : "ZH_CN";
            String redisKey = RedisKeyConstant.XINGPAN_CHART_DATA_KEY + languageType + ":transit:" + constellationId + ":" + requestHash;

            String day = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            // 如果行运日期为空，使用当前时间
            if (StringUtils.isEmpty(transitRequest.getTransitday())) {
                transitRequest.setTransitday(day);
            }

            // 尝试从Redis获取数据
            if (!"1".equals(transitRequest.getIsCorpus())) {
                String chartDataJson = redisService.getString(redisKey);
                if (StringUtils.isNotEmpty(chartDataJson)) {
                    JSONObject chartData = JSONObject.parseObject(chartDataJson);
                    return Result.ok(chartData);
                }
            }
            
            // 获取星座档案信息
            Optional<ConstellationProfile> constellationProfileOptional = constellationMapper.findById(constellationId);
            if (!constellationProfileOptional.isPresent()) {
                return Result.error("星座档案不存在");
            }
            ConstellationProfile constellationProfile = constellationProfileOptional.get();

            // 获取出生地点信息
            CountryCode birthPlace = countryCodeMapper.findById(Long.parseLong(constellationProfile.getBirthPlaceId())).orElse(null);
            if (birthPlace == null) {
                return Result.error("出生地点信息不存在");
            }
            
            // 构建请求参数
            cn.hutool.json.JSONObject parseObj = new cn.hutool.json.JSONObject();
            parseObj.putOpt("access_token", token);
            parseObj.putOpt("planets", transitRequest.getPlanets());
            parseObj.putOpt(transitRequest.getPlanetXsName(), transitRequest.getPlanetXs());
            parseObj.putOpt("virtual", transitRequest.getVirtual());
            parseObj.putOpt(transitRequest.getHSysName(), transitRequest.getHSys());
            parseObj.putOpt(transitRequest.getSvgTypeName(), transitRequest.getSvgType());

            // 处理phase参数，将Map转换为API所需的格式
            if (transitRequest.getPhase() != null && !transitRequest.getPhase().isEmpty()) {
                for (Map.Entry<Integer, Float> entry : transitRequest.getPhase().entrySet()) {
                    parseObj.putOpt("phase[" + entry.getKey() + "]", entry.getValue());
                }
            }

            parseObj.putOpt(transitRequest.getIsCorpusName(), transitRequest.getIsCorpus());

            HttpResponse response = sendJsonPostRequest(url + ApiConstant.API_XINGPAN_CHART_TRANSIT, parseObj.toString());
            String result = response.body();
            response.close();
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.getInteger("code").equals(0)) {
                JSONObject data = jsonObject.getJSONObject("data");
                JSONArray planets = data.getJSONArray("planet");
                for (int i = 0; i < planets.size(); i++) {
                    JSONObject plant = planets.getJSONObject(i);
                    Integer signId = plant.getJSONObject("sign").getInteger("sign_id");
                    Integer houseId = plant.getInteger("house_id");
                    String plantId = plant.getString("code_name");
                    // 根据语言类型选择不同的Redis键
                    String constellationRedisKey = getConstellationRedisKey(transitRequest.getLanguageType());
                    String zodiacRedisKey = getZodiacRedisKey(transitRequest.getLanguageType());
                    String allowRedisKey = getAllowRedisKey(transitRequest.getLanguageType());

                    // 星座
                    String constellationJson = redisService.getString(constellationRedisKey + RedisKeyConstant.XINGPAN_TRANSIT_TYPE + plantId + ":" + signId);
                    String zodiadcJson = redisService.getString(zodiacRedisKey + RedisKeyConstant.XINGPAN_TRANSIT_TYPE + plantId + ":" + houseId);
                    JSONArray corpusArr = new JSONArray();
                    if (StringUtils.isNotEmpty(constellationJson)) {
                        corpusArr.add(JSONObject.parseObject(constellationJson));
                    }
                    if (StringUtils.isNotEmpty(zodiadcJson)) {
                        corpusArr.add(JSONObject.parseObject(zodiadcJson));
                    }
                    plant.put("corpus", corpusArr);
                    // 角度
                    JSONArray planetAllowDegree = plant.getJSONArray("planet_allow_degree");
                    for (int j = 0; j < planetAllowDegree.size(); j++) {
                        JSONObject allowDegreeObj = planetAllowDegree.getJSONObject(j);
                        String allowJson = redisService.getString(allowRedisKey + RedisKeyConstant.XINGPAN_TRANSIT_TYPE + plantId + ":" + allowDegreeObj.getString("code_name") + ":" + allowDegreeObj.get("allow"));
                        allowDegreeObj.put("corpus", JSONObject.parseObject(allowJson));
                    }
                }
                
                // 计算当天结束时间的剩余秒数
                int seconds = getSecondsUntilEndOfDay();
                
                // 将数据存入Redis，设置过期时间为当天结束
                redisService.setString(redisKey, data.toJSONString(), seconds);
                
                return Result.ok(data);
            } else {
                return Result.error(jsonObject.getInteger("code"), jsonObject.getString("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("系统异常");
        }
    }

    private HttpResponse sendPostRequest(String url, String requestData) {
        // 发送POST请求并获取响应
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .body(requestData)
                .timeout(5000)
                .execute();

        if (response.getStatus() != 200) {
            // 如果不是200 OK状态，可以在这里处理异常情况
            throw new RuntimeException("请求失败，HTTP状态码：" + response.getStatus());
        }

        return response;
    }

    private HttpResponse sendJsonPostRequest(String url, String requestData) {
        // 发送POST请求并获取响应
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .body(requestData)
                .timeout(5000)
                .execute();

        if (response.getStatus() != 200) {
            // 如果不是200 OK状态，可以在这里处理异常情况
            throw new RuntimeException("请求失败，HTTP状态码：" + response.getStatus());
        }

        return response;
    }

    /**
     * 计算从当前时间到当天结束的剩余秒数
     * @return 剩余秒数
     */
    private int getSecondsUntilEndOfDay() {
        Calendar cal = Calendar.getInstance();
        Calendar endOfDay = Calendar.getInstance();
        // 增加 1 天，变成明天
        endOfDay.add(Calendar.DATE, 1);
        endOfDay.set(Calendar.HOUR_OF_DAY, 23);
        endOfDay.set(Calendar.MINUTE, 59);
        endOfDay.set(Calendar.SECOND, 59);
        endOfDay.set(Calendar.MILLISECOND, 999);
        return (int)((endOfDay.getTimeInMillis() - cal.getTimeInMillis()) / 1000);
    }

    @Override
    public Result getNatalCorpus(NatalCorpusRequest natalCorpusRequest) {
        try {
            Long constellationId = natalCorpusRequest.getConstellationId();
            Integer type = natalCorpusRequest.getType();
            
            if (constellationId == null || type == null) {
                return Result.error("参数不能为空");
            }
            
            // 检查类型是否合法
            if (type < 0 || type > 2) {
                return Result.error("类型参数不合法");
            }
            
            // 构造NatalRequest对象
            NatalRequest natalRequest = new NatalRequest();
            natalRequest.setConstellationId(constellationId);
            
            // 复制其他参数
            natalRequest.setPlanets(natalCorpusRequest.getPlanets());
            natalRequest.setPlanetXs(natalCorpusRequest.getPlanetXs());
            natalRequest.setVirtual(natalCorpusRequest.getVirtual());
            natalRequest.setHSys(natalCorpusRequest.getHSys() != null ? natalCorpusRequest.getHSys() : "p");
            natalRequest.setLanguageType(natalCorpusRequest.getLanguageType());
            
            // 设置默认phase参数
            if (natalCorpusRequest.getPhase() != null) {
                natalRequest.setPhase(natalCorpusRequest.getPhase());
            } else {
                // 创建默认的phase映射
                Map<Integer, Float> defaultPhaseMap = new HashMap<>();
                defaultPhaseMap.put(0, 0f);
                defaultPhaseMap.put(30, 2f);
                defaultPhaseMap.put(36, 2f);
                defaultPhaseMap.put(45, 2f);
                defaultPhaseMap.put(60, 6f);
                defaultPhaseMap.put(72, 2f);
                defaultPhaseMap.put(90, 6f);
                defaultPhaseMap.put(120, 6f);
                defaultPhaseMap.put(135, 0.5f);
                defaultPhaseMap.put(144, 2f);
                defaultPhaseMap.put(150, 2f);
                defaultPhaseMap.put(180, 6f);
                natalRequest.setPhase(defaultPhaseMap);
            }
            
            // 构建Redis缓存键，与chartNatal方法保持一致（包含语言类型）
            String requestJson = JSONObject.toJSONString(natalRequest);
            String requestHash = String.valueOf(requestJson.hashCode());
            String languageType = natalRequest.getLanguageType() != null ? natalRequest.getLanguageType().name() : "ZH_CN";
            String redisKey = RedisKeyConstant.XINGPAN_CHART_DATA_KEY + languageType + ":" + constellationId + ":" + requestHash;
            
            // 尝试从Redis获取数据
            String chartDataJson = redisService.getString(redisKey);
            JSONObject chartData;
            
            // 如果Redis中没有数据，重新请求并存入Redis
            if (StringUtils.isEmpty(chartDataJson)) {
                // 设置需要语料数据
                natalRequest.setIsCorpus("1");
                
                // 调用chartNatal方法获取星盘数据
                Result chartResult = chartNatal(natalRequest);
                
                // 检查调用结果
                if (!chartResult.isSuccess()) {
                    return chartResult;
                }
                
                // 获取星盘数据
                chartData = JSONObject.parseObject(JSONObject.toJSONString(chartResult.getResult()));
            } else {
                // 解析Redis中的数据
                chartData = JSONObject.parseObject(chartDataJson);
            }
            
            // 提取行星数据
            JSONArray planets = chartData.getJSONArray("planet");
            
            // 根据type过滤语料数据
            JSONArray filteredCorpus = new JSONArray();
            
            if (planets != null) {
                for (int i = 0; i < planets.size(); i++) {
                    JSONObject planet = planets.getJSONObject(i);
                    JSONArray corpus = planet.getJSONArray("corpus");
                    
                    if (corpus != null && !corpus.isEmpty()) {
                        // 根据type提取不同类型的语料
                        if (type == 0) {  // 行星
                            // 提取星座语料数据，通常是第一个
                            if (corpus.size() > 0) {
                                JSONObject corpusItem = corpus.getJSONObject(0);
                                if (corpusItem != null && corpusItem.getString("title") != null &&
                                    isSignCorpus(corpusItem.getString("title"), natalCorpusRequest.getLanguageType())) {
                                    filteredCorpus.add(corpusItem);
                                }
                            }
                        } else if (type == 1) {  // 宫位
                            // 提取宫位语料数据，通常是第二个
                            if (corpus.size() > 1) {
                                JSONObject corpusItem = corpus.getJSONObject(1);
                                if (corpusItem != null && corpusItem.getString("title") != null &&
                                    isHouseCorpus(corpusItem.getString("title"), natalCorpusRequest.getLanguageType())) {
                                    filteredCorpus.add(corpusItem);
                                }
                            } else if (corpus.size() == 1 && corpus.getJSONObject(0).getString("title") != null &&
                                      isHouseCorpus(corpus.getJSONObject(0).getString("title"), natalCorpusRequest.getLanguageType())) {
                                filteredCorpus.add(corpus.getJSONObject(0));
                            }
                        } else if (type == 2) {  // 相位
                            // 提取相位语料
                            JSONArray planetAllowDegree = planet.getJSONArray("planet_allow_degree");
                            if (planetAllowDegree != null) {
                                for (int j = 0; j < planetAllowDegree.size(); j++) {
                                    JSONObject allowDegree = planetAllowDegree.getJSONObject(j);
                                    JSONObject allowCorpus = allowDegree.getJSONObject("corpus");
                                    if (allowCorpus != null) {
                                        filteredCorpus.add(allowCorpus);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // 根据语言类型处理语料数据
            if (natalCorpusRequest.getLanguageType() != null && natalCorpusRequest.getLanguageType().equals(LanguageType.EN)) {
                // 英文语料处理：需要将中文标题转换为英文语料
                filteredCorpus = convertCorpusToEnglish(filteredCorpus);
            }

            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("corpus", filteredCorpus);
            if(natalCorpusRequest.getLanguageType() != null && natalCorpusRequest.getLanguageType().equals(LanguageType.ZH_HANT)){
                result = ConverChinese.convertValuesToTraditionalChinese(result.toJSONString());
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("获取本命盘语料数据失败", e);
            return Result.error("系统异常: " + e.getMessage());
        }
    }

    /**
     * 将中文语料转换为英文语料（优化版本，使用批量查询避免N+1问题）
     * @param chineseCorpus 中文语料数组
     * @return 英文语料数组
     */
    private JSONArray convertCorpusToEnglish(JSONArray chineseCorpus) {
        JSONArray englishCorpus = new JSONArray();

        if (chineseCorpus == null || chineseCorpus.isEmpty()) {
            return englishCorpus;
        }

        // 1. 收集所有需要查询的(type, title)组合
        Set<Map.Entry<Integer, String>> uniquePairs = new HashSet<>();
        for (int i = 0; i < chineseCorpus.size(); i++) {
            JSONObject chineseItem = chineseCorpus.getJSONObject(i);
            if (chineseItem != null) {
                String title = chineseItem.getString("title");
                Integer type = chineseItem.getInteger("type");
                if (StringUtils.isNotBlank(title) && type != null) {
                    uniquePairs.add(new AbstractMap.SimpleEntry<>(type, title));
                }
            }
        }

        // 2. 批量查询英文语料
        Map<String, CorpusAstroEn> englishCorpusMap = new HashMap<>();
        if (!uniquePairs.isEmpty()) {
            // 使用 Specification 构建批量查询
            Specification<CorpusAstroEn> spec = (root, query, cb) -> {
                List<Predicate> predicates = uniquePairs.stream()
                        .map(pair -> cb.and(
                                cb.equal(root.get("type"), pair.getKey()),
                                cb.equal(root.get("title"), pair.getValue())
                        ))
                        .collect(Collectors.toList());
                return cb.or(predicates.toArray(new Predicate[0]));
            };
            List<CorpusAstroEn> corpusAstroEns = corpusAstroEnMapper.findAll(spec);

            // 将结果存入 map 以便快速查找
            for (CorpusAstroEn ca : corpusAstroEns) {
                if (ca.getType() != null && ca.getTitle() != null) {
                    englishCorpusMap.put(ca.getType() + ":" + ca.getTitle(), ca);
                }
            }
        }

        // 3. 转换语料数据
        for (int i = 0; i < chineseCorpus.size(); i++) {
            JSONObject chineseItem = chineseCorpus.getJSONObject(i);
            if (chineseItem == null) {
                continue;
            }

            String title = chineseItem.getString("title");
            Integer type = chineseItem.getInteger("type");

            if (StringUtils.isNotBlank(title) && type != null) {
                String key = type + ":" + title;
                CorpusAstroEn englishCorpusEntity = englishCorpusMap.get(key);

                if (englishCorpusEntity != null) {
                    // 构建英文语料对象
                    JSONObject englishItem = new JSONObject();
                    englishItem.put("id", englishCorpusEntity.getId());
                    englishItem.put("type", englishCorpusEntity.getType());
                    englishItem.put("title", englishCorpusEntity.getTitle());
                    englishItem.put("sentence", englishCorpusEntity.getSentence());

                    // 保留中文字段用于对照（如果存在的话）
                    if (englishCorpusEntity.getTitleCn() != null) {
                        englishItem.put("title_cn", englishCorpusEntity.getTitleCn());
                    }
                    if (englishCorpusEntity.getSentenceCn() != null) {
                        englishItem.put("sentence_cn", englishCorpusEntity.getSentenceCn());
                    }

                    englishCorpus.add(englishItem);
                } else {
                    // 如果没有找到对应的英文语料，保留原始中文数据
                    englishCorpus.add(chineseItem);
                }
            } else {
                // 如果title或type为空，保留原始数据
                englishCorpus.add(chineseItem);
            }
        }

        return englishCorpus;
    }

    /**
     * 根据语言类型获取星座语料的Redis键
     * @param languageType 语言类型
     * @return Redis键前缀
     */
    private String getConstellationRedisKey(LanguageType languageType) {
        if (languageType != null && languageType.equals(LanguageType.EN)) {
            return RedisKeyConstant.XINGPAN_PLANT_CONSTELLATION_EN_KEY;
        }
        return RedisKeyConstant.XINGPAN_PLANT_CONSTELLATION_KEY;
    }

    /**
     * 根据语言类型获取宫位语料的Redis键
     * @param languageType 语言类型
     * @return Redis键前缀
     */
    private String getZodiacRedisKey(LanguageType languageType) {
        if (languageType != null && languageType.equals(LanguageType.EN)) {
            return RedisKeyConstant.XINGPAN_PLANT_ZODIADC_EN_KEY;
        }
        return RedisKeyConstant.XINGPAN_PLANT_ZODIADC_KEY;
    }

    /**
     * 根据语言类型获取相位语料的Redis键
     * @param languageType 语言类型
     * @return Redis键前缀
     */
    private String getAllowRedisKey(LanguageType languageType) {
        if (languageType != null && languageType.equals(LanguageType.EN)) {
            return RedisKeyConstant.XINGPAN_PLANT_ALLOW_EN_KEY;
        }
        return RedisKeyConstant.XINGPAN_PLANT_ALLOW_KEY;
    }

    /**
     * 组合盘
     * @param chartRequest 请求参数
     * @return 组合盘数据
     */
    public Result<JSONObject> chart(ChartRequest chartRequest) {
        try {
            List<Long> constellationIds = chartRequest.getConstellationIds();
            Long constellationId = chartRequest.getConstellationId();

            String day = new SimpleDateFormat("yyyy-MM-dd HH:00:00").format(new Date());
            // 如果行运日期为空，使用当前时间
            if (StringUtils.isEmpty(chartRequest.getTransitday())) {
                chartRequest.setTransitday(day);
            }
            
            // 构建请求参数
            cn.hutool.json.JSONObject parseObj = new cn.hutool.json.JSONObject();

            if(Objects.nonNull(constellationId)){
                // 获取星座档案信息
                Optional<ConstellationProfile> constellationProfileOptional = constellationMapper.findById(constellationId);
                if (!constellationProfileOptional.isPresent()) {
                    return Result.error("星座档案不存在");
                }
                ConstellationProfile constellationProfile = constellationProfileOptional.get();

                // 获取出生地点信息
                CountryCode birthPlace = countryCodeMapper.findById(Long.parseLong(constellationProfile.getBirthPlaceId())).orElse(null);
                if (birthPlace == null) {
                    return Result.error("出生地点信息不存在");
                }
                parseObj.putOpt("longitude", birthPlace.getLon());
                parseObj.putOpt("latitude", birthPlace.getLat());
                parseObj.putOpt("tz", constellationProfile.getTimeZone());
                parseObj.putOpt("birthday", constellationProfile.getBirthdayTime());
            }
            if(Objects.nonNull(constellationIds)){
                // 如果是对比盘B、马克斯盘B、马克斯三限盘B、马克斯次限盘B，颠倒两个档案的顺序
                if (chartRequest.getChartType() == 15 || chartRequest.getChartType() == 19 ||
                    chartRequest.getChartType() == 21 || chartRequest.getChartType() == 23) {
                    Collections.reverse(constellationIds);
                }
                // 获取两个档案的信息
                List<ConstellationProfile> profiles = new ArrayList<>();
                for (Long cId : constellationIds) {
                    Optional<ConstellationProfile> profileOptional = constellationMapper.findById(cId);
                    if (!profileOptional.isPresent()) {
                        return Result.error("星座档案不存在: " + cId);
                    }
                    profiles.add(profileOptional.get());
                }
                // 构建用户列表数据
                JSONArray userList = new JSONArray();
                for (ConstellationProfile profile : profiles) {
                    // 获取出生地点信息
                    CountryCode birthPlace = countryCodeMapper.findById(Long.parseLong(profile.getBirthPlaceId())).orElse(null);
                    if (birthPlace == null) {
                        return Result.error("出生地点信息不存在");
                    }

                    // 创建用户信息对象
                    JSONObject userInfo = new JSONObject();
                    userInfo.put("birthday", profile.getBirthdayTime());
                    userInfo.put("longitude", birthPlace.getLon());
                    userInfo.put("latitude", birthPlace.getLat());
                    userInfo.put("tz", profile.getTimeZone());

                    userList.add(userInfo);
                }
                parseObj.putOpt("user_list", userList);
            }
            // 构建请求参数
            parseObj.putOpt("access_token", token);
            parseObj.putOpt("planets", chartRequest.getPlanets());
            parseObj.putOpt(chartRequest.getPlanetXsName(), chartRequest.getPlanetXs());
            parseObj.putOpt("virtual", chartRequest.getVirtual());
            parseObj.putOpt(chartRequest.getHSysName(), chartRequest.getHSys());
            parseObj.putOpt(chartRequest.getTransitdayName(), chartRequest.getTransitday());
            parseObj.putOpt(chartRequest.getSvgTypeName(), chartRequest.getSvgType());
            
            // 处理phase参数，将Map转换为API所需的格式
            if (chartRequest.getPhase() != null && !chartRequest.getPhase().isEmpty()) {
                for (Map.Entry<Integer, Float> entry : chartRequest.getPhase().entrySet()) {
                    parseObj.putOpt("phase[" + entry.getKey() + "]", entry.getValue());
                }
            }
            
            parseObj.putOpt(chartRequest.getIsCorpusName(), "1");

            // 创建Redis缓存键（包含语言类型）
            String languageType = chartRequest.getLanguageType() != null ? chartRequest.getLanguageType().name() : "ZH_CN";
            String requestHash = parseObj.toString().hashCode() + ":" + chartRequest.getChartType();
            String redisKey = RedisKeyConstant.XINGPAN_CHART_DATA_KEY + languageType + ":" + requestHash;

            String chartDataJson = redisService.getString(redisKey);
            if (StringUtils.isNotEmpty(chartDataJson)) {
                JSONObject chartData = JSONObject.parseObject(chartDataJson);
                return Result.ok(chartData);
            }

            String api = ApiConstant.API_XINGPAN_CHART_NATAL;
            String key = "";
            int seconds = 8 * 3600;
            if(chartRequest.getChartType()==2){
                api = ApiConstant.API_XINGPAN_CHART_TRANSIT;
                key = RedisKeyConstant.XINGPAN_TRANSIT_TYPE;
                seconds = getSecondsUntilEndOfDay();
            }else if(chartRequest.getChartType()==3){
                api = ApiConstant.API_XINGPAN_CHART_COMPOSITE;
                key = RedisKeyConstant.XINGPAN_COMBINATION_TYPE;
            }else if(chartRequest.getChartType()==4){
                api = ApiConstant.API_XINGPAN_CHART_THIRDPROGRESSED;
                key = RedisKeyConstant.XINGPAN_THIRDPROGRESSED_TYPE;
            }else if(chartRequest.getChartType()==5){
                api = ApiConstant.API_XINGPAN_CHART_SECONDPROGRESSED;
                key = RedisKeyConstant.XINGPAN_SECONDARYLIMIT_TYPE;
            }else if(chartRequest.getChartType()==6){
                api = ApiConstant.API_XINGPAN_CHART_LUNARRETURN;
                key = RedisKeyConstant.XINGPAN_LUNARRETURN_TYPE;
            }else if(chartRequest.getChartType()==7){
                api = ApiConstant.API_XINGPAN_CHART_SOLARRETURN;
                key = RedisKeyConstant.XINGPAN_SOLARRETURN_TYPE;
            }else if(chartRequest.getChartType()==8){
                api = ApiConstant.API_XINGPAN_CHART_SOLARARC;
                key = RedisKeyConstant.XINGPAN_SOLARARC_TYPE;
            }else if(chartRequest.getChartType()==9){
                api = ApiConstant.API_XINGPAN_CHART_DEVELOPED;
                key = RedisKeyConstant.XINGPAN_DEVELOPED_TYPE;
            }else if(chartRequest.getChartType()==10){
                api = ApiConstant.API_XINGPAN_CHART_SMALLLIMIT;
                key = RedisKeyConstant.XINGPAN_SMALLLIMIT_TYPE;
            }else if(chartRequest.getChartType()==11){
                api = ApiConstant.API_XINGPAN_CHART_NATALTWELVEPOINTER;
                key = RedisKeyConstant.XINGPAN_NATALTWELVEPOINTER_TYPE;
            }else if(chartRequest.getChartType()==12){
                api = ApiConstant.API_XINGPAN_CHART_NATALTHIRTEENPOINTER;
                key = RedisKeyConstant.XINGPAN_NATALTHIRTEENPOINTER_TYPE;
            }else if(chartRequest.getChartType()==13){
                api = ApiConstant.API_XINGPAN_CHART_CURRENT;
                key = RedisKeyConstant.XINGPAN_CURRENT_TYPE;
            }else if(chartRequest.getChartType()==14){
                api = ApiConstant.API_XINGPAN_CHART_COMPARISION_A;
                key = RedisKeyConstant.XINGPAN_COMPARISION_A_TYPE;
            }else if(chartRequest.getChartType()==15){
                api = ApiConstant.API_XINGPAN_CHART_COMPARISION_B;
                key = RedisKeyConstant.XINGPAN_COMPARISION_B_TYPE;
            }else if(chartRequest.getChartType()==16){
                api = ApiConstant.API_XINGPAN_CHART_COMPOSITETHIRPROGR;
                key = RedisKeyConstant.XINGPAN_COMPOSITETHIRPROGR_TYPE;
            }else if(chartRequest.getChartType()==17){
                api = ApiConstant.API_XINGPAN_CHART_COMPOSITESECONDARY;
                key = RedisKeyConstant.XINGPAN_COMPOSITESECONDARY_TYPE;
            }else if(chartRequest.getChartType()==18){
                api = ApiConstant.API_XINGPAN_CHART_MARKS_A;
                key = RedisKeyConstant.XINGPAN_MARKS_A_TYPE;
            }else if(chartRequest.getChartType()==19){
                api = ApiConstant.API_XINGPAN_CHART_MARKS_B;
                key = RedisKeyConstant.XINGPAN_MARKS_B_TYPE;
            }else if(chartRequest.getChartType()==20){
                api = ApiConstant.API_XINGPAN_CHART_MARKSTHIRPROGR_A;
                key = RedisKeyConstant.XINGPAN_MARKSTHIRPROGR_A_TYPE;
            }else if(chartRequest.getChartType()==21){
                api = ApiConstant.API_XINGPAN_CHART_MARKSTHIRPROGR_B;
                key = RedisKeyConstant.XINGPAN_MARKSTHIRPROGR_B_TYPE;
            }else if(chartRequest.getChartType()==22){
                api = ApiConstant.API_XINGPAN_CHART_MARKSSECPROGR_A;
                key = RedisKeyConstant.XINGPAN_MARKSSECPROGR_A_TYPE;
            }else if(chartRequest.getChartType()==23){
                api = ApiConstant.API_XINGPAN_CHART_MARKSSECPROGR_B;
                key = RedisKeyConstant.XINGPAN_MARKSSECPROGR_B_TYPE;
            }else if(chartRequest.getChartType()==24){
                api = ApiConstant.API_XINGPAN_CHART_TIMESMIDPOINT;
                key = RedisKeyConstant.XINGPAN_TIMESMIDPOINT_TYPE;
            }else if(chartRequest.getChartType()==25){
                api = ApiConstant.API_XINGPAN_CHART_TIMESMIDPOINTTHIRPROGR;
                key = RedisKeyConstant.XINGPAN_TIMESMIDPOINTTHIRPROGR_TYPE;
            }else if(chartRequest.getChartType()==26){
                api = ApiConstant.API_XINGPAN_CHART_TIMESMIDPOINTSECPROGR;
                key = RedisKeyConstant.XINGPAN_TIMESMIDPOINTSECPROGR_TYPE;
            }

            HttpResponse response = sendJsonPostRequest(url + api, parseObj.toString());
            String result = response.body();
            response.close();
            JSONObject jsonObject = JSONObject.parseObject(result);
            
            if (jsonObject.getInteger("code").equals(0)) {
                JSONObject data = jsonObject.getJSONObject("data");
                
                // 处理语料数据
                JSONArray planets = data.getJSONArray("planet");
                JSONArray planetsSecond = data.getJSONArray("planet_second");
                if(Objects.nonNull(planetsSecond)){
                    planets.addAll(planetsSecond);
                }
                if (planets != null) {
                    for (int i = 0; i < planets.size(); i++) {
                        JSONObject planet = planets.getJSONObject(i);

                        // 处理星座和宫位语料
                        Integer signId = planet.getJSONObject("sign").getInteger("sign_id");
                        Integer houseId = planet.getInteger("house_id");
                        String planetId = planet.getString("code_name");

                        // 根据语言类型选择不同的Redis键
                        String constellationRedisKey = getConstellationRedisKey(chartRequest.getLanguageType());
                        String zodiacRedisKey = getZodiacRedisKey(chartRequest.getLanguageType());
                        String allowRedisKey = getAllowRedisKey(chartRequest.getLanguageType());

                        // 获取组合盘特定的语料
                        String constellationJson = redisService.getString(constellationRedisKey + key + planetId + ":" + signId);
                        String zodiacJson = redisService.getString(zodiacRedisKey + key + planetId + ":" + houseId);

                        JSONArray corpusArr = new JSONArray();
                        if (StringUtils.isNotEmpty(constellationJson)) {
                            JSONObject corpusItem = JSONObject.parseObject(constellationJson);
                            corpusItem.put("type", "planet_sign");
                            corpusArr.add(corpusItem);
                        }
                        if (StringUtils.isNotEmpty(zodiacJson)) {
                            JSONObject corpusItem = JSONObject.parseObject(zodiacJson);
                            corpusItem.put("type", "planet_house");
                            corpusArr.add(corpusItem);
                        }
                        planet.put("corpus", corpusArr);

                        // 处理相位语料
                        JSONArray planetAllowDegree = planet.getJSONArray("planet_allow_degree");
                        if (planetAllowDegree != null) {
                            for (int j = 0; j < planetAllowDegree.size(); j++) {
                                JSONObject allowDegreeObj = planetAllowDegree.getJSONObject(j);
                                String allowJson = redisService.getString(allowRedisKey + key + planetId + ":" + allowDegreeObj.getString("code_name") + ":" + allowDegreeObj.get("allow"));
                                JSONObject corpusItem = JSONObject.parseObject(allowJson);
                                if (corpusItem != null) {
                                    corpusItem.put("type", "planet_aspect");
                                }
                                allowDegreeObj.put("corpus", corpusItem);
                            }
                        }
                    }
                }

                // 将数据存入Redis，设置过期时间
                redisService.setString(redisKey, data.toJSONString(), seconds);
                
                return Result.ok(data);
            } else {
                return Result.error(jsonObject.getInteger("code"), jsonObject.getString("msg"));
            }
        } catch (Exception e) {
            log.error("获取组合盘数据失败", e);
            return Result.error("系统异常: " + e.getMessage());
        }
    }

    /**
     * 获取组合盘语料数据
     * @param chartRequest 请求参数
     * @return 组合盘语料数据
     */
    public Result<JSONObject> getCorpus(ChartRequest chartRequest) {
        try {
            int type = chartRequest.getType();
            chartRequest.setType(null);
            // 调用组合盘方法获取星盘数据
            Result<JSONObject> chartResult = chart(chartRequest);

            // 检查调用结果
            if (!chartResult.isSuccess()) {
                return chartResult;
            }

            // 获取星盘数据
            JSONObject chartData = JSONObject.parseObject(JSONObject.toJSONString(chartResult.getResult()));
            // 提取行星数据
            JSONArray planets = chartData.getJSONArray("planet");
            JSONArray planetsSecond = chartData.getJSONArray("planet_second");
            if(Objects.nonNull(planetsSecond)){
                planets = planetsSecond;
            }

            // 根据type过滤语料数据
            JSONArray filteredCorpus = new JSONArray();
            
            if (planets != null) {
                for (int i = 0; i < planets.size(); i++) {
                    JSONObject planet = planets.getJSONObject(i);
                    JSONArray corpus = planet.getJSONArray("corpus");
                    
                    if (corpus != null && !corpus.isEmpty()) {
                        // 根据type提取不同类型的语料
                        if (type == 0) {  // 行星
                            // 提取星座语料数据，通常是第一个
                            if (corpus.size() > 0) {
                                JSONObject corpusItem = corpus.getJSONObject(0);
                                if (corpusItem != null && corpusItem.getString("title") != null &&
                                    isSignCorpus(corpusItem.getString("title"), chartRequest.getLanguageType())) {
                                    filteredCorpus.add(corpusItem);
                                }
                            }
                        } else if (type == 1) {  // 宫位
                            // 提取宫位语料数据，通常是第二个
                            if (corpus.size() > 1) {
                                JSONObject corpusItem = corpus.getJSONObject(1);
                                if (corpusItem != null && corpusItem.getString("title") != null &&
                                    isHouseCorpus(corpusItem.getString("title"), chartRequest.getLanguageType())) {
                                    filteredCorpus.add(corpusItem);
                                }
                            } else if (corpus.size() == 1 && corpus.getJSONObject(0).getString("title") != null &&
                                      isHouseCorpus(corpus.getJSONObject(0).getString("title"), chartRequest.getLanguageType())) {
                                filteredCorpus.add(corpus.getJSONObject(0));
                            }
                        } else if (type == 2) {  // 相位
                            // 提取相位语料
                            JSONArray planetAllowDegree = planet.getJSONArray("planet_allow_degree");
                            if (planetAllowDegree != null) {
                                for (int j = 0; j < planetAllowDegree.size(); j++) {
                                    JSONObject allowDegree = planetAllowDegree.getJSONObject(j);
                                    JSONObject allowCorpus = allowDegree.getJSONObject("corpus");
                                    if (allowCorpus != null) {
                                        filteredCorpus.add(allowCorpus);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 注意：不需要在这里处理语言类型转换，因为chart方法已经根据语言类型
            // 从正确的Redis缓存中获取了对应语言的语料数据

            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("corpus", filteredCorpus);

            return Result.ok(result);
        } catch (Exception e) {
            log.error("获取数据失败", e);
            return Result.error("系统异常: " + e.getMessage());
        }
    }

    /**
     * 获取星盘数据（整合本命盘、行运盘和组合盘）
     * @param chartRequest 请求参数
     * @return 星盘数据
     */
    @Override
    public Result<JSONObject> getChart(ChartRequest chartRequest) {
        try {
            // 获取盘类型
            Integer chartType = chartRequest.getChartType();
            if (chartType == null) {
                return Result.error("盘类型不能为空");
            }

            return chart(chartRequest);
        } catch (Exception e) {
            log.error("获取星盘数据失败", e);
            return Result.error("系统异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取星盘语料数据（整合本命盘语料、行运盘语料和组合盘语料）
     * @param chartRequest 请求参数
     * @return 星盘语料数据
     */
    @Override
    public Result<JSONObject> getChartCorpus(ChartRequest chartRequest) {
        try {
            // 获取盘类型
            Integer chartType = chartRequest.getChartType();
            if (chartType == null) {
                return Result.error("盘类型不能为空");
            }

            // 设置默认phase参数
            if (chartRequest.getPhase() != null) {
                chartRequest.setPhase(chartRequest.getPhase());
            } else {
                // 创建默认的phase映射
                Map<Integer, Float> defaultPhaseMap = new HashMap<>();
                defaultPhaseMap.put(0, 0f);
                defaultPhaseMap.put(30, 2f);
                defaultPhaseMap.put(36, 2f);
                defaultPhaseMap.put(45, 2f);
                defaultPhaseMap.put(60, 6f);
                defaultPhaseMap.put(72, 2f);
                defaultPhaseMap.put(90, 6f);
                defaultPhaseMap.put(120, 6f);
                defaultPhaseMap.put(135, 0.5f);
                defaultPhaseMap.put(144, 2f);
                defaultPhaseMap.put(150, 2f);
                defaultPhaseMap.put(180, 6f);
                chartRequest.setPhase(defaultPhaseMap);
            }
            return getCorpus(chartRequest);
        } catch (Exception e) {
            log.error("获取星盘语料数据失败", e);
            return Result.error("系统异常: " + e.getMessage());
        }
    }

    /**
     * 星相日历
     * @param astroCalendarRequest 请求参数
     * @return 星相日历数据
     */
    @Override
    public Result getAstroCalendar(AstroCalendarRequest astroCalendarRequest) {
        try {
            LocalDateTime startDateTime;
            LocalDateTime endDateTime;
            boolean isSingleDay = false;
            
            // 判断是否按单天查询
            if (StringUtils.isNotBlank(astroCalendarRequest.getDate())) {
                // 如果提供了具体日期，则按照具体日期查询
                LocalDateTime specificDate = LocalDateTime.parse(astroCalendarRequest.getDate() + " 00:00:00", 
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                startDateTime = specificDate;
                endDateTime = specificDate.withHour(23).withMinute(59).withSecond(59);
                isSingleDay = true;
            } else {
                // 按照年月查询
                // 解析年月
                YearMonth yearMonth = YearMonth.parse(astroCalendarRequest.getYearMonth(), DateTimeFormatter.ofPattern("yyyy-MM"));
                
                // 获取开始日期
                startDateTime = yearMonth.atDay(1).atStartOfDay();
                
                endDateTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
            }

            // 根据用户时区调整查询时间范围（数据以+8保存）
            int timeZone = astroCalendarRequest.getTimeZone() == null ? 8 : astroCalendarRequest.getTimeZone();
            int offsetHours = timeZone - 8;
            LocalDateTime queryStart = startDateTime;
            LocalDateTime queryEnd = endDateTime;
            if (offsetHours != 0) {
                queryStart = startDateTime.minusHours(offsetHours);
                queryEnd = endDateTime.minusHours(offsetHours);
            }
            List<AstroCalendarEvent> events = astroCalendarEventMapper.findAllByEventDatetimeBetween(queryStart, queryEnd);
            
            // 如果查询的是整月且无数据，先抓取数据再处理
            if (events.isEmpty() || (events.size()<10 && !isSingleDay)) {
                // 仅当查询整月数据时才自动抓取
                if (!isSingleDay) {
                    // 从startDateTime解析年月
                    int year = startDateTime.getYear();
                    int month = startDateTime.getMonthValue();
                    log.info("没有找到{}-{}月份的星历数据，正在抓取...", year, month);
                    fetchAndSaveAstroCalendar(year, month);
                    // 重新查询数据
                    events = astroCalendarEventMapper.findAllByEventDatetimeBetween(queryStart, queryEnd);
                } else if (events.isEmpty()) {
                    // 如果是查询单天且无数据，返回空结果
                    if (isSingleDay) {
                        // 返回空对象
                        JSONObject emptyDayData = new JSONObject();
                        emptyDayData.put("titleShort", "");
                        emptyDayData.put("events", new JSONArray());
                        emptyDayData.put("date", astroCalendarRequest.getDate());
                        return Result.ok(emptyDayData);
                    } else {
                        // 返回空数组
                        return Result.ok(new JSONArray());
                    }
                }
            }

            // 1. 批量查询语料以提高效率
            Map<String, String> sentenceMap = new HashMap<>();
            Map<String, String> titleMap = new HashMap<>();
            if (!events.isEmpty()) {
                // 提取唯一的 (type, title) 组合
                Set<Map.Entry<Integer, String>> uniquePairs = events.stream()
                        .filter(e -> e.getType() != null && StringUtils.isNotBlank(e.getTitle()))
                        .map(e -> new AbstractMap.SimpleEntry<>(e.getType(), e.getTitle()))
                        .collect(Collectors.toSet());

                if (!uniquePairs.isEmpty()) {
                    // 根据语言类型选择不同的数据表
                    if (astroCalendarRequest.getLanguageType() != null && astroCalendarRequest.getLanguageType().equals(LanguageType.EN)) {
                        // 查询英文语料
                        Specification<CorpusAstroEn> specEn = (root, query, cb) -> {
                            List<Predicate> predicates = uniquePairs.stream()
                                    .map(pair -> cb.and(
                                            cb.equal(root.get("type"), pair.getKey()),
                                            cb.equal(root.get("titleCn"), pair.getValue())
                                    ))
                                    .collect(Collectors.toList());
                            return cb.or(predicates.toArray(new Predicate[0]));
                        };
                        List<CorpusAstroEn> corpusAstroEns = corpusAstroEnMapper.findAll(specEn);

                        // 将结果存入 map 以便快速查找
                        for (CorpusAstroEn ca : corpusAstroEns) {
                            if (ca.getType() != null && ca.getTitleCn() != null) {
                                sentenceMap.put(ca.getType() + ":" + ca.getTitleCn(), StringUtils.defaultString(ca.getSentence()));
                                titleMap.put(ca.getType() + ":" + ca.getTitleCn(), StringUtils.defaultString(ca.getTitle()));
                            }
                        }
                    } else {
                        // 查询中文语料
                        Specification<CorpusAstro> spec = (root, query, cb) -> {
                            List<Predicate> predicates = uniquePairs.stream()
                                    .map(pair -> cb.and(
                                            cb.equal(root.get("type"), pair.getKey()),
                                            cb.equal(root.get("title"), pair.getValue())
                                    ))
                                    .collect(Collectors.toList());
                            return cb.or(predicates.toArray(new Predicate[0]));
                        };
                        List<CorpusAstro> corpusAstros = corpusAstroMapper.findAll(spec);

                        // 将结果存入 map 以便快速查找
                        for (CorpusAstro ca : corpusAstros) {
                            if (ca.getType() != null && ca.getTitle() != null) {
                                sentenceMap.put(ca.getType() + ":" + ca.getTitle(), StringUtils.defaultString(ca.getSentence()));
                            }
                        }
                    }
                }
            }

            // 根据用户时区分组与摘要
            DateTimeFormatter groupingFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            Map<String, List<JSONObject>> eventsByDate = new LinkedHashMap<>();
            Map<String, Map<String, Object>> summaryByDate = new HashMap<>();
            for (AstroCalendarEvent event : events) {
                LocalDateTime displayDateTime = event.getEventDatetime();
                if (offsetHours != 0) {
                    displayDateTime = displayDateTime.plusHours(offsetHours);
                }
                String dateKey = displayDateTime.format(groupingFormatter);
                eventsByDate.computeIfAbsent(dateKey, k -> new ArrayList<>()).add(formatEventForResponse(event, sentenceMap));
            }
            // 生成摘要，优先级 4>2>9>6
            for (Map.Entry<String, List<JSONObject>> entry : eventsByDate.entrySet()) {
                String dateKey = entry.getKey();
                List<JSONObject> dayEvents = entry.getValue();
                int[] priority = new int[]{4, 2, 9, 6};
                String titleShort = "";
                Integer type = null;
                for (int p : priority) {
                    for (JSONObject obj : dayEvents) {
                        if (String.valueOf(p).equals(obj.getString("type"))) {
                            String ts = obj.getString("titleShort");
                            if (org.apache.commons.lang3.StringUtils.isNotBlank(ts)) {
                                titleShort = ts;
                                type = p;
                                break;
                            }
                        }
                    }
                    if (type != null) { break; }
                }
                Map<String, Object> sm = new HashMap<>();
                sm.put("titleShort", titleShort);
                sm.put("type", type == null ? "" : String.valueOf(type));
                summaryByDate.put(dateKey, sm);
            }
            
            // 获取行星配置
            SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(
                    SystemConfigConstant.MOBILE_CONFIG_KEY, SystemConfigConstant.PLANET_SETTING_CONFIG_TYPE);
            JSONArray planetSettings = JSONArray.parseArray(systemConfig.getContext());
            if(astroCalendarRequest.getLanguageType().equals(LanguageType.EN)){
                for (int i = 0; i < planetSettings.size(); i++) {
                    JSONObject jsonObject = planetSettings.getJSONObject(i);
                    jsonObject.put("planetChinese",jsonObject.getString("planetEnglish"));
                }
            }

            // 根据查询类型构造响应数据
            if (isSingleDay) {
                // 单天查询 - 返回当天数据对象
                String dateStr = astroCalendarRequest.getDate();
                JSONObject dayData = new JSONObject();
                
                // 查找当天的标题摘要（按用户时区分组）
                String titleShort = "";
                String type = "";
                Map<String, Object> sm = summaryByDate.get(dateStr);
                if (sm != null) {
                    titleShort = (String) sm.get("titleShort");
                    type = String.valueOf(sm.get("type"));
                }

                List<JSONObject> eventsList = eventsByDate.getOrDefault(dateStr, new ArrayList<>());
                if(astroCalendarRequest.getLanguageType().equals(LanguageType.EN)){
                    for (JSONObject jsonObject : eventsList) {
                        jsonObject.put("title",titleMap.get(jsonObject.getString("type")+":"+jsonObject.getString("title")));
                    }
                }
                dayData.put("titleShort", titleShort != null ? titleShort : "");
                dayData.put("type", type != null ? type : "");
                dayData.put("events", eventsList);
                dayData.put("date", dateStr);
                dayData.put("planetSettings", planetSettings);
                
                return Result.ok(dayData);
            } else {
                // 月查询 - 返回数组
                JSONArray monthData = new JSONArray();
                // 仅英文月查询时需要对非空的titleShort做翻译
                JSONObject astroCfg = null;
                JSONObject planetSymbolCfg = null;
                JSONArray signAbbrCfg = null;
                if (astroCalendarRequest.getLanguageType().equals(LanguageType.EN)) {
                    try {
                        SystemConfig astroNewCfg = systemConfigMapper.findByConfigKeyAndConfigType(
                                SystemConfigConstant.MOBILE_CONFIG_KEY, "astrolabe_new");
                        if (astroNewCfg != null && org.apache.commons.lang3.StringUtils.isNotBlank(astroNewCfg.getContext())) {
                            astroCfg = JSONObject.parseObject(astroNewCfg.getContext());
                            planetSymbolCfg = astroCfg.getJSONObject("planetSymbol");
                            signAbbrCfg = astroCfg.getJSONArray("signAbbr");
                        }
                    } catch (Exception ignore) {
                    }
                }
                
                // 根据事件摘要构建响应数组（按用户时区分组）
                List<String> sortedDates = new ArrayList<>(summaryByDate.keySet());
                Collections.sort(sortedDates);
                for (String date : sortedDates) {
                    Map<String, Object> summary = summaryByDate.get(date);
                    String titleShort = (String) summary.get("titleShort");
                    String type = String.valueOf(summary.get("type"));

                    List<JSONObject> eventsList = eventsByDate.getOrDefault(date, new ArrayList<>());
                    if(astroCalendarRequest.getLanguageType().equals(LanguageType.EN)){
                        for (JSONObject jsonObject : eventsList) {
                            jsonObject.put("title",titleMap.get(jsonObject.getString("type")+":"+jsonObject.getString("title")));
                        }
                        // 翻译day层级titleShort（仅当不为空字符串时）
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(titleShort)) {
                            try {
                                int t = Integer.parseInt(type);
                                // Type6 新月 -> NM；Type9 满月 -> FM
                                if (t == 6) {
                                    titleShort = "NM";
                                } else if (t == 9) {
                                    titleShort = "FM";
                                } else if (t == 4 && planetSymbolCfg != null) {
                                    // 逆/顺行：符号 + R/D
                                    JSONObject evt = eventsList.stream()
                                            .filter(e -> "4".equals(String.valueOf(e.getInteger("type"))))
                                            .findFirst().orElse(null);
                                    if (evt != null) {
                                        String planetCode = evt.getString("planetCode");
                                        String symbol = planetSymbolCfg.getString(planetCode);
                                        String title = evt.getString("title");
                                        String rd = (title != null && title.toLowerCase().contains("retrograde")) ? "R" : "D";
                                        if (org.apache.commons.lang3.StringUtils.isNotBlank(symbol)) {
                                            titleShort = symbol + rd;
                                        }
                                    }
                                } else if (t == 2 && planetSymbolCfg != null && signAbbrCfg != null) {
                                    // 入宫：行星符号 + 星座abbr 例：☽Sag
                                    JSONObject evt = eventsList.stream()
                                            .filter(e -> "2".equals(String.valueOf(e.getInteger("type"))))
                                            .findFirst().orElse(null);
                                    if (evt != null) {
                                        String planetCode = evt.getString("planetCode");
                                        String symbol = planetSymbolCfg.getString(planetCode);
                                        Integer signIdx = null;
                                        if (evt.get("sign") != null) {
                                            try { signIdx = Integer.parseInt(String.valueOf(evt.get("sign"))); } catch (Exception ignore) { }
                                        }
                                        String abbr = (signIdx != null && signIdx >= 0 && signIdx < signAbbrCfg.size()) ? signAbbrCfg.getString(signIdx) : null;
                                        if (org.apache.commons.lang3.StringUtils.isNotBlank(symbol) && org.apache.commons.lang3.StringUtils.isNotBlank(abbr)) {
                                            titleShort = symbol + abbr;
                                        }
                                    }
                                }
                            } catch (Exception ignore) {
                            }
                        }
                    }
                    JSONObject dayObj = new JSONObject();
                    dayObj.put("date", date);
                    dayObj.put("titleShort", titleShort != null ? titleShort : "");
                    dayObj.put("type", type != null ? type : "");
                    dayObj.put("events", eventsList);
                    dayObj.put("planetSettings", planetSettings);
                    monthData.add(dayObj);
                }
                // 兼容原有返回结构：{ data: [...], planetSettings: [...] }
                JSONObject responseData = new JSONObject();
                responseData.put("data", monthData);
                responseData.put("planetSettings", planetSettings);
                return Result.ok(responseData);
            }
        } catch (Exception e) {
            log.error("获取星相日历数据异常", e);
            return Result.error("系统异常");
        }
    }

    private JSONObject formatEventForResponse(AstroCalendarEvent event, Map<String, String> sentenceMap) {
        // Convert entity to JSONObject
        JSONObject eventJson = (JSONObject) JSONObject.toJSON(event);

        // Remove fields that should not be in the response
        eventJson.remove("eventDatetime");
        eventJson.remove("timestampVal");

        // Format date and time fields based on original time
        LocalDateTime originalDateTime = event.getEventDatetime();
        eventJson.put("date", originalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        eventJson.put("time", event.getEventTime());

        // Explicitly format allowCha to avoid scientific notation
        if (event.getAllowCha() != null) {
            eventJson.put("allowCha", new BigDecimal(event.getAllowCha().toString()).toPlainString());
        }

        // Add sentence field
        String sentence = "";
        if (event.getType() != null && StringUtils.isNotBlank(event.getTitle())) {
            sentence = sentenceMap.getOrDefault(event.getType() + ":" + event.getTitle(), "");
        }
        eventJson.put("sentence", sentence);
        
        // Create a new JSONObject to filter out nulls
        JSONObject filteredJson = new JSONObject(true);
        for (Map.Entry<String, Object> entry : eventJson.entrySet()) {
            if (entry.getValue() != null) {
                filteredJson.put(entry.getKey(), entry.getValue());
            }
        }
        return filteredJson;
    }

    @Override
    @Transactional
    public void fetchAndSaveAstroCalendar(int year, int month) {
        // 构建请求参数
        String requestData = "access_token=" + token +
                "&year=" + year +
                "&month=" + month;

        // 发送请求
        HttpResponse response = sendPostRequest(url + "/sign/astrocalendar", requestData);
        String result = response.body();
        response.close();

        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject.getInteger("code") != null && jsonObject.getInteger("code").equals(0)) {
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null || data.isEmpty()) {
                log.warn("No data returned from API for {}-{}", year, month);
                return;
            }

            YearMonth yearMonth = YearMonth.of(year, month);
            LocalDateTime startOfMonth = yearMonth.atDay(1).atStartOfDay();
            LocalDateTime endOfMonth = yearMonth.atEndOfMonth().atTime(23, 59, 59);

            // Adjust deletion range to account for the +8h offset
            astroCalendarEventMapper.deleteAllByEventDatetimeBetween(startOfMonth.plusHours(8), endOfMonth.plusHours(8));
            log.info("Deleted existing astro events for {}-{}", year, month);

            List<AstroCalendarEvent> eventsToSave = new ArrayList<>();

            for (String day : data.keySet()) {
                JSONArray dayEvents = data.getJSONArray(day);
                for (int i = 0; i < dayEvents.size(); i++) {
                    JSONObject eventJson = dayEvents.getJSONObject(i);
                    AstroCalendarEvent event = new AstroCalendarEvent();

                    String dateStr = eventJson.getString("date");
                    if (StringUtils.isNotBlank(dateStr)) {
                        LocalDateTime originalDateTime = LocalDateTime.parse(dateStr, FLEXIBLE_DATETIME_FORMATTER);
                        // Add 8 hours before saving
                        event.setEventDatetime(originalDateTime.plusHours(8));
                        // Set eventTime from the original datetime's HH:mm
                        event.setEventTime(originalDateTime.plusHours(8).format(DateTimeFormatter.ofPattern("HH:mm")));
                    }

                    event.setAllow(eventJson.getInteger("allow"));
                    event.setType(eventJson.getInteger("type"));
                    event.setTitle(eventJson.getString("title"));
                    event.setTitleShort(eventJson.getString("title_short"));
                    event.setTimestampVal(eventJson.getLong("timestamp"));
                    event.setLongitude(eventJson.getDouble("longitude"));
                    event.setEndDate(eventJson.getString("end_date"));
                    event.setAllowCha(eventJson.getDouble("allow_cha"));

                    // Handle aspect-like events
                    if (eventJson.containsKey("planet_code1")) {
                        event.setPlanetCode1(eventJson.getString("planet_code1"));
                        event.setPlanetCode2(eventJson.getString("planet_code2"));
                        event.setPlanetEnglish1(eventJson.getString("planet_english1"));
                        event.setPlanetChinese1(eventJson.getString("planet_chinese1"));
                        event.setPlanetEnglish2(eventJson.getString("planet_english2"));
                        event.setPlanetChinese2(eventJson.getString("planet_chinese2"));
                        event.setPlanetFont1(eventJson.getString("planet_font1"));
                        event.setPlanetFont2(eventJson.getString("planet_font2"));
                    }

                    // Handle sign-change events
                    if (eventJson.containsKey("planet_code")) {
                        event.setPlanetCode(eventJson.getString("planet_code"));
                        event.setSign(eventJson.getString("sign"));
                        event.setSignEnglish(eventJson.getString("sign_english"));
                        event.setSignFont(eventJson.getString("sign_font"));
                        event.setSignChinese(eventJson.getString("sign_chinese"));
                    }

                    eventsToSave.add(event);
                }
            }

            if (!eventsToSave.isEmpty()) {
                astroCalendarEventMapper.saveAll(eventsToSave);
                log.info("Saved {} new astro events for {}-{}", eventsToSave.size(), year, month);
            }

        } else {
            log.error("Failed to fetch astro calendar data for {}-{}. Response: {}", year, month, result);
            throw new RuntimeException("Failed to fetch astro calendar data. code: " + jsonObject.getInteger("code") + ", msg: " + jsonObject.getString("msg"));
        }
    }

    /**
     * 发送缓存的分析结果流
     * @param cachedResult 缓存的分析结果
     * @param emitter SSE发射器
     */
    @Override
    public void sendCachedAnalysisStream(JSONObject cachedResult, SseEmitter emitter) {
        CompletableFuture.runAsync(() -> {
            try {
                if (cachedResult == null || !cachedResult.containsKey("content")) {
                    sendSseEventSafely(emitter, SseEmitter.event().name("error").data("缓存的分析结果为空或格式不正确"));
                    emitter.complete();
                    return;
                }
                
                String content = cachedResult.getString("content");
                
                // 如果是整个响应结果，直接一次性发送
                sendSseEventSafely(emitter, SseEmitter.event().data(content));
                log.info("已从缓存发送完整分析结果，内容长度: {}", content.length());
                
            } catch (Exception e) {
                log.error("发送缓存的分析结果时发生异常", e);
                try {
                    sendSseEventSafely(emitter, SseEmitter.event().name("error").data("发送缓存数据时发生异常: " + e.getMessage()));
                } catch (Exception ex) {
                    log.error("发送错误消息时发生异常", ex);
                }
            } finally {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.debug("完成SSE发射器时发生异常(可能已经关闭)", e.getMessage());
                }
            }
        });
    }

    /**
     * 获取DeepSeek分析结果流，并通过回调函数返回完整结果
     * @param chartData 星盘数据
     * @param emitter SSE发射器
     * @param resultCallback 分析完成后的回调函数，用于返回完整结果
     */
    @Override
    public void getDeepSeekAnalysisStream(JSONObject chartData, SseEmitter emitter, Consumer<JSONObject> resultCallback) {
        // 调用带增量更新的方法，忽略增量更新回调
        getDeepSeekAnalysisStream(chartData, emitter, delta -> {
            // 不做任何处理，忽略增量更新
        }, resultCallback);
    }
    
    /**
     * 为了兼容现有代码，保留旧的方法，内部调用新方法
     */
    @Override
    public void getDeepSeekAnalysisStream(JSONObject chartData, SseEmitter emitter) {
        // 调用带增量更新的方法，忽略所有回调
        getDeepSeekAnalysisStream(chartData, emitter, delta -> {
            // 不做任何处理，忽略增量更新
        }, result -> {
            // 不做任何处理，忽略完整结果
        });
    }
    
    /**
     * 安全地发送SSE事件，处理已关闭的发射器
     * @param emitter SSE发射器
     * @param event 要发送的事件
     * @return 是否成功发送
     */
    private boolean sendSseEventSafely(SseEmitter emitter, SseEmitter.SseEventBuilder event) {
        try {
            emitter.send(event);
            return true;
        } catch (Exception e) {
            // 检查异常是否与已关闭的发射器有关
            if (e.getMessage() != null && 
                (e.getMessage().contains("complete") || 
                 e.getMessage().contains("closed") || 
                 e.getMessage().contains("已完成"))) {
                // 静默处理已关闭的发射器
                log.debug("SSE发射器已关闭: {}", e.getMessage());
            } else {
                log.error("发送SSE事件异常", e);
            }
            return false;
        }
    }
    
    /**
     * 获取DeepSeek分析结果流
     * @param chartData 星盘数据
     * @param emitter SSE发射器
     */
    @Override
    public Result getStarInfoById(StarInfoByIdRequest starInfoByIdRequest) {
        try {
            Long id = starInfoByIdRequest.getId();
            if (id == null) {
                return Result.error("ID不能为空");
            }
            
            // 根据ID查询事件
            Optional<AstroCalendarEvent> eventOptional = astroCalendarEventMapper.findById(id);
            if (!eventOptional.isPresent()) {
                return Result.error("未找到对应的事件");
            }
            
            AstroCalendarEvent event = eventOptional.get();
            Integer type = event.getType();
            String title = event.getTitle();

            if (type == null || StringUtils.isBlank(title)) {
                return Result.error("事件类型或标题为空，无法查询语料");
            }

            JSONObject result = new JSONObject();

            // 根据语言类型选择不同的数据表
            if (starInfoByIdRequest.getLanguageType() != null && starInfoByIdRequest.getLanguageType().equals(LanguageType.EN)) {
                // 查询英文语料
                Optional<CorpusAstroEn> corpusAstroEnOptional = corpusAstroEnMapper.findByTypeAndTitleCn(type, title);
                if (!corpusAstroEnOptional.isPresent()) {
                    return Result.error("未找到相关信息");
                }

                CorpusAstroEn corpusAstroEn = corpusAstroEnOptional.get();
                result.put("current", JSONObject.parseObject(corpusAstroEn.getChartContentJson()));
                result.put("markdown", corpusAstroEn.getChartContentMarkdown());
                result.put("title", corpusAstroEn.getTitle());
                // 保留中文字段用于对照
                result.put("title_cn", corpusAstroEn.getTitleCn());
            } else {
                // 查询中文语料
                Optional<CorpusAstro> corpusAstroOptional = corpusAstroMapper.findByTypeAndTitle(type, title);
                if (!corpusAstroOptional.isPresent()) {
                    return Result.error("未找到相关信息");
                }

                CorpusAstro corpusAstro = corpusAstroOptional.get();
                result.put("current", JSONObject.parseObject(corpusAstro.getChartContentJson()));
                result.put("markdown", corpusAstro.getChartContentMarkdown());
                result.put("title", corpusAstro.getTitle());
            }

            return Result.ok(result);
            
        } catch (Exception e) {
            log.error("根据ID获取星体信息失败", e);
            return Result.error("获取星体信息失败");
        }
    }

    /**
     * 获取行星逆顺事件对
     * @param planetRetrogradePairsRequest 请求参数
     * @return 行星逆顺事件对数据
     */
    @Override
    public Result getPlanetRetrogradePairs(PlanetRetrogradePairsRequest planetRetrogradePairsRequest) {
        try {
            // 时区偏移（数据以+8保存）
            int timeZone = planetRetrogradePairsRequest.getTimeZone() == null ? 8 : planetRetrogradePairsRequest.getTimeZone();
            int offsetHours = timeZone - 8;
            // 验证参数
            if (StringUtils.isBlank(planetRetrogradePairsRequest.getPlanetCode())) {
                return Result.error("行星代号不能为空");
            }
            
            // 获取行星配置
            SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(
                SystemConfigConstant.MOBILE_CONFIG_KEY, SystemConfigConstant.PLANET_SETTING_CONFIG_TYPE);
                
            JSONArray planetSettings = JSONArray.parseArray(systemConfig.getContext());

            // 默认查询水星
            String planetCode = planetRetrogradePairsRequest.getPlanetCode();
            String planetChinese = "水星"; // 默认值
            
            // 从配置中找到对应的行星名称
            for (int i = 0; i < planetSettings.size(); i++) {
                JSONObject planet = planetSettings.getJSONObject(i);
                if (planet.getString("planetId").equals(planetCode)) {
                    planetChinese = planet.getString("planetChinese");
                    break;
                }
            }

            if(planetRetrogradePairsRequest.getLanguageType().equals(LanguageType.EN)){
                for (int i = 0; i < planetSettings.size(); i++) {
                    JSONObject jsonObject = planetSettings.getJSONObject(i);
                    jsonObject.put("planetChinese",jsonObject.getString("planetEnglish"));
                }
            }
            
            // 根据行星中文名称的第一个字构造逆行/顺行标题
            String firstChar = planetChinese.substring(0, 1); // 取第一个字
            String retrogradeTitle = firstChar + "逆";
            String directTitle = firstChar + "顺";
            
            // 使用Mapper方法查询行星逆顺事件对
            List<Object[]> planetPairs = astroCalendarEventMapper.findPlanetRetrogradePairs(
                planetCode, retrogradeTitle, directTitle);
            
            // 构建结果
            JSONObject responseData = new JSONObject();
            if (planetPairs != null && !planetPairs.isEmpty()) {
                JSONArray planetPairResults = new JSONArray();
                
                for (Object[] row : planetPairs) {
                    JSONObject pair = new JSONObject();
                    pair.put("id", row[0]);
                    
                    // 解析并按用户时区调整日期时间
                    LocalDateTime rawStart;
                    LocalDateTime rawEnd;
                    if (row[2] instanceof java.sql.Timestamp) {
                        rawStart = ((java.sql.Timestamp) row[2]).toLocalDateTime();
                    } else if (row[2] instanceof LocalDateTime) {
                        rawStart = (LocalDateTime) row[2];
                    } else {
                        rawStart = LocalDateTime.parse(row[2].toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    }
                    if (row[7] instanceof java.sql.Timestamp) {
                        rawEnd = ((java.sql.Timestamp) row[7]).toLocalDateTime();
                    } else if (row[7] instanceof LocalDateTime) {
                        rawEnd = (LocalDateTime) row[7];
                    } else {
                        rawEnd = LocalDateTime.parse(row[7].toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    }
                    LocalDateTime displayStart = offsetHours != 0 ? rawStart.plusHours(offsetHours) : rawStart;
                    LocalDateTime displayEnd = offsetHours != 0 ? rawEnd.plusHours(offsetHours) : rawEnd;

                    // 根据语言类型处理日期格式（使用时区调整后的时间）
                    if (planetRetrogradePairsRequest.getLanguageType() != null && planetRetrogradePairsRequest.getLanguageType().equals(LanguageType.EN)) {
                        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MMMM", Locale.ENGLISH);
                        DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("d", Locale.ENGLISH);
                        DateTimeFormatter yearFormatter = DateTimeFormatter.ofPattern("yyyy", Locale.ENGLISH);

                        String startMonthStr = displayStart.format(monthFormatter);
                        String startDayStr = displayStart.format(dayFormatter);
                        String startYearStr = displayStart.format(yearFormatter);

                        String endMonthStr = displayEnd.format(monthFormatter);
                        String endDayStr = displayEnd.format(dayFormatter);
                        String endYearStr = displayEnd.format(yearFormatter);

                        pair.put("start_date", startMonthStr + " " + startDayStr + ", " + startYearStr);
                        pair.put("end_date", endMonthStr + " " + endDayStr + ", " + endYearStr);
                    } else {
                        DateTimeFormatter zhFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                        pair.put("start_date", displayStart.format(zhFormatter));
                        pair.put("end_date", displayEnd.format(zhFormatter));
                    }

                    // 输出标准化的日期时间字符串
                    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    pair.put("start_datetime", displayStart.format(dtf));
                    pair.put("short_title", row[3]);
                    pair.put("title", row[4]);
                    pair.put("end_id", row[5]);
                    pair.put("end_datetime", displayEnd.format(dtf));
                    pair.put("end_short_title", row[8]);
                    pair.put("end_title", row[9]);

                    // 新增逻辑：查询并添加语料信息
                    String title = (String) row[4];
                    Integer type = (Integer) row[10]; // 获取type字段

                    if (type != null && StringUtils.isNotBlank(title)) {
                        if(planetRetrogradePairsRequest.getLanguageType() != null && planetRetrogradePairsRequest.getLanguageType().equals(LanguageType.EN)){
                            Optional<CorpusAstroEn> corpusAstroOptional = corpusAstroEnMapper.findByTypeAndTitleCn(type, title);
                            if (corpusAstroOptional.isPresent()) {
                                CorpusAstroEn corpusAstro = corpusAstroOptional.get();
                                if (StringUtils.isNotEmpty(corpusAstro.getChartContentJson())) {
                                    pair.put("corpus", JSONObject.parseObject(corpusAstro.getChartContentJson()));
                                    pair.put("title", corpusAstro.getTitle());
                                    pair.put("end_title", corpusAstroEnMapper.findByTitleCn(row[9].toString()).getTitle());
                                }
                            }
                        }else{
                            Optional<CorpusAstro> corpusAstroOptional = corpusAstroMapper.findByTypeAndTitle(type, title);
                            if (corpusAstroOptional.isPresent()) {
                                CorpusAstro corpusAstro = corpusAstroOptional.get();
                                if (StringUtils.isNotEmpty(corpusAstro.getChartContentJson())) {
                                    pair.put("corpus", JSONObject.parseObject(corpusAstro.getChartContentJson()));
                                }
                            }
                        }
                    }

                    planetPairResults.add(pair);
                }
                
                // 添加到响应数据中
                responseData.put("planet_pairs", planetPairResults);
            } else {
                // 如果没有找到数据，返回空数组
                responseData.put("planet_pairs", new JSONArray());
            }
            
            return Result.ok(responseData);
        } catch (Exception e) {
            log.error("获取行星逆顺事件对数据异常", e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result signPush(SignPushRequest signPushRequest) {
        String requestData = "access_token=" + token +
                "&sign=" + signPushRequest.getSign() +
                "&birthday=" + signPushRequest.getBirthday() +
                "&years=" + signPushRequest.getYears() +
                "&unit=" + signPushRequest.getUnit();
        HttpResponse response = sendPostRequest(url + "/chart/signpush", requestData);
        String result = response.body();
        response.close();
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject.getInteger("code").equals(0)) {
            return Result.ok(jsonObject.getJSONArray("data"));
        } else {
            return Result.error(jsonObject.getInteger("code"), jsonObject.getString("msg"));
        }
    }

    @Override
    public void getDeepSeekAnalysisStream(JSONObject chartData, SseEmitter emitter, 
                                         Consumer<JSONObject> incrementalCallback,
                                         Consumer<JSONObject> resultCallback) {
        // 调用带繁体转换参数的方法，默认不转换
        getDeepSeekAnalysisStream(chartData, emitter, incrementalCallback, resultCallback, false);
    }
    
    @Override
    public void getDeepSeekAnalysisStream(JSONObject chartData, SseEmitter emitter,
                                         Consumer<JSONObject> incrementalCallback,
                                         Consumer<JSONObject> resultCallback,
                                         boolean needTraditionalChinese) {
        // 调用带语言类型参数的方法，默认为简体中文
        getDeepSeekAnalysisStream(chartData, emitter, incrementalCallback, resultCallback, needTraditionalChinese, LanguageType.ZH_CN);
    }

    public void getDeepSeekAnalysisStream(JSONObject chartData, SseEmitter emitter,
                                         Consumer<JSONObject> incrementalCallback,
                                         Consumer<JSONObject> resultCallback,
                                         boolean needTraditionalChinese,
                                         LanguageType languageType) {
        CompletableFuture.runAsync(() -> {
            StringBuilder fullResponse = new StringBuilder();
            boolean apiCalled = false;
            String cacheKey = null;
            final long[] lastCacheUpdateTime = {System.currentTimeMillis()};
            
            try {
                long startTime = System.currentTimeMillis();
                log.info("开始处理星盘AI分析请求，带增量更新，繁体转换: {}", needTraditionalChinese);
                
                // Extract all corpus items from the chart data
                JSONArray allCorpusItems = new JSONArray();
                if (chartData.containsKey("planet_second") && chartData.getJSONArray("planet_second") != null) {
                    JSONArray planets = chartData.getJSONArray("planet_second");
                    for (Object p : planets) {
                        JSONObject planet = (JSONObject) p;
                        if (planet.containsKey("corpus") && planet.getJSONArray("corpus") != null) {
                            allCorpusItems.addAll(planet.getJSONArray("corpus"));
                        }
                        if (planet.containsKey("planet_allow_degree") && planet.getJSONArray("planet_allow_degree") != null) {
                            JSONArray allowDegrees = planet.getJSONArray("planet_allow_degree");
                            for (Object ad : allowDegrees) {
                                JSONObject allowDegree = (JSONObject) ad;
                                if (allowDegree.containsKey("corpus") && allowDegree.getJSONObject("corpus") != null) {
                                    JSONObject corpusItem = allowDegree.getJSONObject("corpus");
                                    if (corpusItem.getString("type") == null) {
                                        corpusItem.put("type", "planet_aspect");
                                    }
                                    allCorpusItems.add(corpusItem);
                                }
                            }
                        }
                    }
                }

                // 根据语言类型构建不同的标题
                StringBuilder planetsInfo = new StringBuilder(getPlanetSignLabel(languageType));
                StringBuilder housesInfo = new StringBuilder(getPlanetHouseLabel(languageType));
                StringBuilder aspectsInfo = new StringBuilder(getPlanetAspectLabel(languageType));

                boolean hasPlanets = false, hasHouses = false, hasAspects = false;

                for (Object itemObj : allCorpusItems) {
                    JSONObject item = (JSONObject) itemObj;
                    if (item == null) continue;

                    String type = item.getString("type");
                    String title = item.getString("title");
                    if (title == null) continue;

                    if (type == null) {
                        if (title.contains("落入")) {
                            type = "planet_sign";
                        } else if (title.contains("宫")) {
                            type = "planet_house";
                        }
                    }

                    if ("planet_sign".equals(type)) {
                        planetsInfo.append(title).append("、");
                        hasPlanets = true;
                    } else if ("planet_house".equals(type)) {
                        housesInfo.append(title).append("、");
                        hasHouses = true;
                    } else if ("planet_aspect".equals(type)) {
                        aspectsInfo.append(title).append("、");
                        hasAspects = true;
                    }
                }

                StringBuilder promptContent = new StringBuilder();
                if (hasPlanets) {
                    promptContent.append(planetsInfo.substring(0, planetsInfo.length() - 1)).append("\n");
                }
                if (hasHouses) {
                    promptContent.append(housesInfo.substring(0, housesInfo.length() - 1)).append("\n");
                }
                if (hasAspects) {
                    promptContent.append(aspectsInfo.substring(0, aspectsInfo.length() - 1));
                }

                if (promptContent.length() == 0) {
                    sendSseEventSafely(emitter, SseEmitter.event().name("error").data(getNoContentErrorMessage(languageType)));
                    return;
                }

                // Caching logic - Read
                String promptHash = String.valueOf(promptContent.toString().hashCode());
                cacheKey = RedisKeyConstant.DEEPSEEK_ANALYSIS_KEY + promptHash;
                log.info("星盘分析缓存键: {}, 提示内容哈希: {}", cacheKey, promptHash);
                
                String cachedResult = null;
                try {
                    cachedResult = redisService.getString(cacheKey);
                    if (StringUtils.isNotEmpty(cachedResult)) {
                        log.info("命中Redis缓存，直接返回结果，内容长度: {}, 缓存键: {}", cachedResult.length(), cacheKey);
                    }
                } catch (Exception e) {
                    log.error("Redis缓存读取异常", e);
                }
                
                if (StringUtils.isNotEmpty(cachedResult)) {
                    try {
                        // 处理繁体转换
                        String contentToSend = cachedResult;
                        if (needTraditionalChinese) {
                            com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap zhConvertBootstrap = 
                                com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap.newInstance();
                            contentToSend = zhConvertBootstrap.toTraditional(contentToSend);
                        }
                        
                        sendSseEventSafely(emitter, SseEmitter.event().data(contentToSend));
                        // 返回缓存的结果给回调函数
                        JSONObject resultJson = new JSONObject();
                        resultJson.put("content", contentToSend);
                        resultCallback.accept(resultJson);
                        return;
                    } catch (Exception e) {
                        log.error("Error sending cached result via SSE, falling back to API", e);
                    }
                }

                log.info("Redis缓存未命中，调用DeepSeek API");
                
                // 记录请求参数，用于缓存生成
                String requestHash = String.valueOf(promptContent.toString().hashCode());
                
                JSONObject requestBody = new JSONObject();
                requestBody.put("model", "deepseek-chat");
                requestBody.put("stream", true);

                JSONArray messages = new JSONArray();
                JSONObject systemMessage = new JSONObject();
                systemMessage.put("role", "system");
                systemMessage.put("content", getSystemPrompt(languageType));

                JSONObject userMessage = new JSONObject();
                userMessage.put("role", "user");
                String prompt = getUserPrompt(languageType, promptContent.toString());
                userMessage.put("content", prompt);

                messages.add(systemMessage);
                messages.add(userMessage);
                requestBody.put("messages", messages);
                
                apiCalled = true;
                // 设置一个标志，标记SSE连接是否仍然活跃
                final boolean[] isEmitterActive = {true};
                final String finalCacheKey = cacheKey;
                
                try {
                    // 使用HttpClientUtil进行API调用
                    HttpClientUtil.streamDeepSeekAPI(
                        deepseekApiUrl,
                        deepseekApiKey,
                        requestBody.toJSONString(),
                        data -> {
                            try {
                                JSONObject responseJson = JSONObject.parseObject(data);
                                if (responseJson.containsKey("choices")) {
                                    JSONArray choices = responseJson.getJSONArray("choices");
                                    if (!choices.isEmpty()) {
                                        JSONObject choice = choices.getJSONObject(0);
                                        if (choice.containsKey("delta") && choice.getJSONObject("delta").containsKey("content")) {
                                            String content = choice.getJSONObject("delta").getString("content");
                                            
                                            // 发送增量更新给增量回调函数
                                            JSONObject deltaUpdate = new JSONObject();
                                            deltaUpdate.put("delta", content);
                                            incrementalCallback.accept(deltaUpdate);
                                            
                                            // 如果emitter仍然活跃，发送内容
                                            if (isEmitterActive[0]) {
                                                // 处理繁体转换
                                                String contentToSend = content;
                                                if (needTraditionalChinese) {
                                                    com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap zhConvertBootstrap = 
                                                        com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap.newInstance();
                                                    contentToSend = zhConvertBootstrap.toTraditional(contentToSend);
                                                }
                                                
                                                boolean sendSuccess = sendSseEventSafely(emitter, SseEmitter.event().data(contentToSend));
                                                if (!sendSuccess) {
                                                    isEmitterActive[0] = false;
                                                    log.info("SSE连接已关闭，停止发送但继续收集响应");
                                                }
                                            }
                                            
                                            fullResponse.append(content);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                log.error("解析流式JSON响应异常: {}", e.getMessage());
                            }
                        }
                    );
                } catch (Exception e) {
                    log.error("DeepSeek API调用异常: {}", e.getMessage());
                    throw e;
                }

                // Caching logic - Write
                if (fullResponse.length() > 0) {
                    try {
                        // 确保响应有最小长度，避免缓存部分不完整的响应
                        if (fullResponse.length() < 100) {
                            log.warn("响应内容过短，可能不完整，不进行缓存: {} 字符", fullResponse.length());
                        } else {
                            int secondsUntilEndOfDay = getSecondsUntilEndOfDay();
                            String responseContent = fullResponse.toString();
                            
                            // 处理繁体转换
                            String contentForCallback = responseContent;
                            if (needTraditionalChinese) {
                                com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap zhConvertBootstrap = 
                                    com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap.newInstance();
                                contentForCallback = zhConvertBootstrap.toTraditional(responseContent);
                            }
                            
                            // 写入Redis缓存（缓存原始内容，不缓存转换后的内容）
                            redisService.setString(cacheKey, responseContent, secondsUntilEndOfDay);
                            log.info("AI分析完成，结果已缓存到Redis，内容长度: {}, 缓存键: {}", responseContent.length(), cacheKey);
                            
                            // 返回完整结果给回调函数（如果需要繁体则返回转换后的内容）
                            JSONObject resultJson = new JSONObject();
                            resultJson.put("content", contentForCallback);
                            resultCallback.accept(resultJson);
                        }
                    } catch (Exception e) {
                        log.error("缓存结果时发生异常", e);
                        // 仍然尝试返回结果
                        JSONObject resultJson = new JSONObject();
                        resultJson.put("content", fullResponse.toString());
                        resultCallback.accept(resultJson);
                    }
                } else {
                    log.warn("分析结果为空，不进行缓存");
                }
                
                log.info("星盘AI分析请求处理完成，总耗时: {} ms", System.currentTimeMillis() - startTime);

            } catch (Exception e) {
                log.error("星盘AI分析处理异常", e);
                try {
                    sendSseEventSafely(emitter, SseEmitter.event().name("error").data("调用AI分析服务时发生异常: " + e.getMessage()));
                } catch (Exception ex) {
                    log.error("Error sending SSE error message", ex);
                }
                
                // 如果API已调用但异常中断，仍然尝试缓存已收集的内容
                if (apiCalled && fullResponse.length() > 0 && cacheKey != null) {
                    try {
                        int secondsUntilEndOfDay = getSecondsUntilEndOfDay();
                        String responseContent = fullResponse.toString();
                        
                        // 处理繁体转换
                        String contentForCallback = responseContent;
                        if (needTraditionalChinese) {
                            com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap zhConvertBootstrap = 
                                com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap.newInstance();
                            contentForCallback = zhConvertBootstrap.toTraditional(responseContent);
                        }
                        
                        // 缓存原始内容
                        redisService.setString(cacheKey, responseContent, secondsUntilEndOfDay);
                        log.info("尽管有异常，仍然缓存了部分结果，内容长度: {}", fullResponse.length());
                        
                        // 返回转换后的内容
                        JSONObject resultJson = new JSONObject();
                        resultJson.put("content", contentForCallback);
                        resultCallback.accept(resultJson);
                    } catch (Exception ex) {
                        log.error("缓存部分结果失败", ex);
                    }
                }
            } finally {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.debug("完成SSE发射器时发生异常(可能已经关闭)", e.getMessage());
                }
            }
        });
    }

    /**
     * 根据语言类型获取行星星座标签
     */
    private String getPlanetSignLabel(LanguageType languageType) {
        if (languageType != null && languageType.equals(LanguageType.EN)) {
            return "Planets in Signs: ";
        }
        return "行星落入星座：";
    }

    /**
     * 根据语言类型获取行星宫位标签
     */
    private String getPlanetHouseLabel(LanguageType languageType) {
        if (languageType != null && languageType.equals(LanguageType.EN)) {
            return "Planets in Houses: ";
        }
        return "行星落入宫位：";
    }

    /**
     * 根据语言类型获取行星相位标签
     */
    private String getPlanetAspectLabel(LanguageType languageType) {
        if (languageType != null && languageType.equals(LanguageType.EN)) {
            return "Planetary Aspects: ";
        }
        return "行星相位：";
    }

    /**
     * 根据语言类型获取系统提示词
     */
    private String getSystemPrompt(LanguageType languageType) {
        if (languageType != null && languageType.equals(LanguageType.EN)) {
            return "You are a professional astrologer who fully understands the twelve zodiac signs and their meanings, understands the influence of planetary positions, houses, and aspects on human life, and can accurately interpret astrological predictions. The analysis report should achieve a shocking feeling of \"How does it know me so well?!\"";
        }
        return "你是一个专业的星盘分析师，你充分理解十二星座及其含义，理解行星位置、宫位、相位对人类生活的影响，能够准确解读占星预测，报告分析最好达到\"它怎么会懂我？！\"的震撼感。";
    }

    /**
     * 根据语言类型获取用户提示词
     */
    private String getUserPrompt(LanguageType languageType, String promptContent) {
        if (languageType != null && languageType.equals(LanguageType.EN)) {
            return "You are a professional astrologer who fully understands the twelve zodiac signs and their meanings, understands the influence of planetary positions, houses, and aspects on human life, and can accurately interpret astrological predictions. The analysis report should achieve a shocking feeling of \"How does it know me so well?!\". From \"my\" perspective, help me analyze today's fortune. The content I provide is:\n" + promptContent +
                    "\n\nThe analysis report includes the following categories:\n" +
                    "Today's Fortune: Use easy-to-understand language, no need to mention constellations and houses, no less than 500 words\n" +
                    "Love Fortune: Use easy-to-understand language, no need to mention constellations and houses, no less than 300 words\n" +
                    "Career Fortune: Use easy-to-understand language, no need to mention constellations and houses, no less than 300 words\n" +
                    "Wealth Fortune: Use easy-to-understand language, no need to mention constellations and houses, no less than 300 words\n" +
                    "Health Fortune: Use easy-to-understand language, no need to mention constellations and houses, no less than 300 words\n\n" +
                    "Please output the analysis report directly in clean MarkDown format without any code blocks, explanations, or additional comments. Start directly with the content.";
        }
        return "你是一个专业的星盘分析师，你充分理解十二星座及其含义，理解行星位置、宫位、相位对人类生活的影响，能够准确解读占星预测，报告分析最好达到\"它怎么会懂我？！\"的震撼感。以\"我\"的视角，帮我分析今日运势，我提供的内容是：\n" + promptContent +
                "\n报告分析包含以下分类：\n" +
                "今日运势：使用通俗易懂的语言，无需提及星座和宫位，不少于500字\n" +
                "爱情运势：使用通俗易懂的语言，无需提及星座和宫位，不少于300字\n" +
                "事业运势：使用通俗易懂的语言，无需提及星座和宫位，不少于300字\n" +
                "财富运势：使用通俗易懂的语言，无需提及星座和宫位，不少于300字\n" +
                "健康运势：使用通俗易懂的语言，无需提及星座和宫位，不少于300字\n\n" +
                "以上分析报告以MarkDown的形式输出";
    }

    /**
     * 根据语言类型获取错误消息
     */
    private String getNoContentErrorMessage(LanguageType languageType) {
        if (languageType != null && languageType.equals(LanguageType.EN)) {
            return "Unable to extract valid corpus content from chart data";
        }
        return "未能从星盘数据中提取到有效语料内容";
    }

    /**
     * 检查标题是否为星座语料（多语言支持）
     * @param title 语料标题
     * @param languageType 语言类型
     * @return 是否为星座语料
     */
    private boolean isSignCorpus(String title, LanguageType languageType) {
        if (title == null) {
            return false;
        }

        if (languageType != null && languageType.equals(LanguageType.EN)) {
            // 英文匹配：检查是否包含 "in" 关键词，这是英文星座语料的特征
            // 例如："Sun in Aries", "Moon in Taurus"
            return title.toLowerCase().contains(" in ");
        } else {
            // 中文匹配：检查是否包含"落入"
            return title.contains("落入");
        }
    }

    /**
     * 检查标题是否为宫位语料（多语言支持）
     * @param title 语料标题
     * @param languageType 语言类型
     * @return 是否为宫位语料
     */
    private boolean isHouseCorpus(String title, LanguageType languageType) {
        if (title == null) {
            return false;
        }

        if (languageType != null && languageType.equals(LanguageType.EN)) {
            // 英文匹配：检查是否包含 "House" 关键词
            // 例如："Sun in 1st House", "Moon in 2nd House"
            return title.toLowerCase().contains("house");
        } else {
            // 中文匹配：检查是否包含"宫"
            return title.contains("宫");
        }
    }
}
