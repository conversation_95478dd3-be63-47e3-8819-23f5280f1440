package com.lhx.birthday.service.impl;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.Strings;
import com.github.stuxuhai.jpinyin.PinyinHelper;
import com.lhx.birthday.entity.*;
import com.lhx.birthday.enums.*;
import com.lhx.birthday.mapper.*;
import com.lhx.birthday.request.profile.ProfileBaseRequest;
import com.lhx.birthday.request.profile.ProfileUpdateSortRequest;
import com.lhx.birthday.response.profile.ProfileGroupResponse;
import com.lhx.birthday.service.IProfileService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.StringUtils;
import com.lhx.birthday.vo.profile.ProfileGroupVO;
import com.lhx.birthday.vo.profile.ProfileVO;
import com.lhx.birthday.vo.UserInfoVO;
import com.tyme.culture.Constellation;
import com.tyme.culture.Zodiac;
import com.tyme.lunar.LunarDay;
import com.tyme.lunar.LunarHour;
import com.tyme.lunar.LunarMonth;
import com.tyme.sixtycycle.EarthBranch;
import com.tyme.solar.SolarDay;
import com.xkzhangsan.time.LunarDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import javax.persistence.criteria.Predicate;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.lhx.birthday.constant.SettingConstant.*;

@Service
@Slf4j
public class ProfileServiceImpl implements IProfileService {

    @Autowired
    private ProfileMapper profileMapper;

    @Autowired
    private ProfilePromptMapper profilePromptMapper;

    @Autowired
    private ProfileGroupRelMapper profileGroupRelMapper;

    @Autowired
    private ProfileGroupMapper profileGroupMapper;

    @Autowired
    private BirthdayInfoMapper birthdayInfoMapper;

    @Autowired
    private IUserInfoService userInfoService;

    @Value("${oss.staticDomain}")
    private String domain;

    @Override
    public ProfileVO addProfile(ProfileBaseRequest profileBaseRequest) {
        Profile addProfile = new Profile();
        BeanUtils.copyProperties(profileBaseRequest,addProfile);
        addProfile.setSort(0);
        addProfile.setCreateTime(LocalDateTime.now());
        addProfile.setModifyTime(LocalDateTime.now());
        if(Objects.isNull(addProfile.getAvatar()) || StringUtils.isEmpty(addProfile.getAvatar())){
            addProfile.setAvatar(domain+DEFAULT_PROFILE_AVATAR);
        }
        // 公历
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime birthdayTime = LocalDateTime.parse(profileBaseRequest.getBirthdayTime(),formatter);
        addProfile.setSolarTime(birthdayTime);
        // 公历转换农历
        SolarDay solarDay = SolarDay.fromYmd(birthdayTime.getYear(),birthdayTime.getMonthValue(), birthdayTime.getDayOfMonth());
        LunarDay lunarDay = solarDay.getLunarDay();
        String lunarStr = lunarDay.getYear() + "/" + lunarDay.getMonth() + "/" + lunarDay.getDay() + " "+birthdayTime.toLocalTime().toString();
        addProfile.setLunarTime(lunarStr);
        // 计算生肖星座
        Zodiac zodiac = lunarDay.getYearSixtyCycle().getEarthBranch().getZodiac();
        Constellation constellation = lunarDay.getSolarDay().getConstellation();
        addProfile.setZodiacSignType(ZodiacSignType.values()[constellation.getIndex()]);
        addProfile.setZodiacType(ZodiacType.values()[zodiac.getIndex()]);
        Profile profile = profileMapper.saveAndFlush(addProfile);
        // 保存档案通知信息
        ProfilePrompt addProfilePrompt = new ProfilePrompt();
        BeanUtils.copyProperties(profileBaseRequest,addProfilePrompt);
        addProfilePrompt.setProfileId(profile.getId());
        // 计算出今年生日
        addProfilePrompt.setSolarTime(addProfile.getSolarTime());
        addProfilePrompt.setLunarTime(addProfile.getLunarTime());
        addProfilePrompt.setCreateTime(LocalDateTime.now());
        addProfilePrompt.setModifyTime(LocalDateTime.now());
        // 保存下次生日信息
        Map<String, String> solarMap = calculateNextBirthday(profile.getSolarTime().toLocalDate(),LocalDate.now());
        String solarNextBirthday = solarMap.get("nextBirthday");
        addProfilePrompt.setNextSolarTime(solarNextBirthday);
        String[] lunarSpl = profile.getLunarTime().split(" ")[0].split("/");
        Map<String, String> lunarMap = calculateNextLunarBirthday(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]),LocalDate.now());
        String lunarNextBirthday = lunarMap.get("nextBirthday");
        addProfilePrompt.setNextLunarTime(lunarNextBirthday);
        profilePromptMapper.saveAndFlush(addProfilePrompt);
        // 保存分组信息
        if(Objects.nonNull(profileBaseRequest.getGroupIds()) && !StringUtils.isEmpty(profileBaseRequest.getGroupIds())){
            for (String id : profileBaseRequest.getGroupIds().split(",")) {
                ProfileGroupRel profileGroupRel = ProfileGroupRel.builder()
                        .profileGroupId(Long.valueOf(id))
                        .profileId(profile.getId())
                        .userId(profile.getUserId())
                        .createTime(LocalDateTime.now())
                        .modifyTime(LocalDateTime.now())
                        .build();
                profileGroupRelMapper.saveAndFlush(profileGroupRel);
            }
        }
        ProfileVO profileVO = new ProfileVO();
        BeanUtils.copyProperties(profile, profileVO);
        // 新增档案推送
        if(Objects.nonNull(profileBaseRequest.getRegistrationId())){
            userInfoService.initPushData(Collections.singletonList(addProfilePrompt), ActionUnit.ADD,null);
        }
        return profileVO;
    }

    @Override
    @Transactional
    public ProfileVO updateProfile(ProfileBaseRequest profileBaseRequest) {
        Optional<Profile> profileOptional = profileMapper.findById(profileBaseRequest.getId());
        if(profileOptional.isPresent()){
            Profile profile = profileOptional.get();
            profile.setModifyTime(LocalDateTime.now());
            profile.setName(profileBaseRequest.getName());
            profile.setGender(profileBaseRequest.getGender());
            profile.setAvatar(profileBaseRequest.getAvatar());
            profile.setBirthdayType(profileBaseRequest.getBirthdayType());
            // 公历
            LunarDay lunarDay;
//            if(profileBaseRequest.getBirthdayType().equals(BirthdayType.SOLAR)){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime birthdayTime = LocalDateTime.parse(profileBaseRequest.getBirthdayTime(),formatter);
                profile.setSolarTime(birthdayTime);
                // 公历转换农历
                SolarDay solarDay = SolarDay.fromYmd(birthdayTime.getYear(),birthdayTime.getMonthValue(), birthdayTime.getDayOfMonth());
                lunarDay = solarDay.getLunarDay();
                String lunarStr = lunarDay.getYear() + "/"+lunarDay.getMonth()+"/"+lunarDay.getDay()+" "+birthdayTime.toLocalTime().toString();
                profile.setLunarTime(lunarStr);
//            }
            // 农历
//            else{
//                profile.setLunarTime(profileBaseRequest.getBirthdayTime());
                // 农历转公历
//                String[] lunarSpl = profileBaseRequest.getBirthdayTime().split(" ")[0].split("-");
//                String[] timeSpl = profileBaseRequest.getBirthdayTime().split(" ")[1].split(":");
//                lunarDay = LunarDay.fromYmd(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]));
//                SolarDay solarDay = lunarDay.getSolarDay();
//                profile.setSolarTime(LocalDateTime.of(LocalDate.of(solarDay.getYear(),solarDay.getMonth(),solarDay.getDay()),LocalTime.of(Integer.parseInt(timeSpl[0]),Integer.parseInt(timeSpl[1]))));
//            }
            profile.setTimeZone(profileBaseRequest.getTimeZone());
            profile.setRemarkInfo(profileBaseRequest.getRemarkInfo());
            if(Objects.nonNull(profileBaseRequest.getRelationship())){
                profile.setRelationship(profileBaseRequest.getRelationship());
            }
            // 计算生肖星座
            Zodiac zodiac = lunarDay.getYearSixtyCycle().getEarthBranch().getZodiac();
            Constellation constellation = lunarDay.getSolarDay().getConstellation();
            profile.setZodiacSignType(ZodiacSignType.values()[constellation.getIndex()]);
            profile.setZodiacType(ZodiacType.values()[zodiac.getIndex()]);
            profileMapper.saveAndFlush(profile);
            // 保存档案通知信息
            ProfilePrompt profilePrompt = profilePromptMapper.findByProfileId(profileBaseRequest.getId());
            ProfilePrompt oldProfilePrompt = new ProfilePrompt();
            BeanUtils.copyProperties(profilePrompt,oldProfilePrompt);
            Long profileId = profilePrompt.getId();
            BeanUtils.copyProperties(profileBaseRequest,profilePrompt);
            profilePrompt.setSolarTime(profile.getSolarTime());
            profilePrompt.setLunarTime(profile.getLunarTime());
            profilePrompt.setModifyTime(LocalDateTime.now());
            profilePrompt.setId(profileId);
            // 保存下次生日信息
            Map<String, String> solarMap = calculateNextBirthday(profile.getSolarTime().toLocalDate(),LocalDate.now());
            String solarNextBirthday = solarMap.get("nextBirthday");
            profilePrompt.setNextSolarTime(solarNextBirthday);
            String[] lunarSpl = profile.getLunarTime().split(" ")[0].split("/");
            Map<String, String> lunarMap = calculateNextLunarBirthday(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]),LocalDate.now());
            String lunarNextBirthday = lunarMap.get("nextBirthday");
            profilePrompt.setNextLunarTime(lunarNextBirthday);
            profilePromptMapper.saveAndFlush(profilePrompt);
            // 保存分组信息
            if(Objects.nonNull(profileBaseRequest.getGroupIds()) && !StringUtils.isEmpty(profileBaseRequest.getGroupIds())){
                // 获取当前用户的已有分组
                List<ProfileGroupRel> existingGroups = profileGroupRelMapper.findByProfileId(profile.getId());

                // 将新分组ID转换为集合
                Set<Long> newGroupIds = Arrays.stream(profileBaseRequest.getGroupIds().split(","))
                        .map(Long::valueOf)
                        .collect(Collectors.toSet());
                // 处理新增和删除的分组
                for (Long id : newGroupIds) {
                    // 如果分组不存在，则创建新的关系
                    if (existingGroups.stream().noneMatch(group -> group.getProfileGroupId().equals(id))) {
                        ProfileGroupRel profileGroupRel = ProfileGroupRel.builder()
                                .profileGroupId(id)
                                .profileId(profile.getId())
                                .userId(profile.getUserId())
                                .createTime(LocalDateTime.now())
                                .modifyTime(LocalDateTime.now())
                                .build();
                        profileGroupRelMapper.saveAndFlush(profileGroupRel);
                    }
                }

                // 删除不再存在的分组
                for (ProfileGroupRel existingGroup : existingGroups) {
                    if (!newGroupIds.contains(existingGroup.getProfileGroupId())) {
                        profileGroupRelMapper.delete(existingGroup);
                    }
                }
            }else{
                // 如果没有分组ID，考虑清空当前用户的分组关系
                profileGroupRelMapper.deleteByProfileId(profile.getId());
            }
            ProfileVO profileVO = new ProfileVO();
            BeanUtils.copyProperties(profile, profileVO);
            // 更新档案推送
            if(Objects.nonNull(profileBaseRequest.getRegistrationId())){
                userInfoService.initPushData(Collections.singletonList(oldProfilePrompt), ActionUnit.DELETE,null);
                userInfoService.initPushData(Collections.singletonList(profilePrompt), ActionUnit.ADD,null);
            }
            return profileVO;
        }
        return null;
    }

    @Override
    public int updateSort(ProfileUpdateSortRequest profileUpdateSortRequest) {
        String idsStr = profileUpdateSortRequest.getIds();
        List<Long> idOrderList = Arrays.stream(idsStr.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Profile> profiles = profileMapper.findAllById(idOrderList);

        Map<Long, Profile> profileMap = new LinkedHashMap<>();
        for (Profile profile : profiles) {
            profileMap.put(profile.getId(), profile);
        }

        List<Profile> sortedProfiles = idOrderList.stream()
                .map(profileMap::get)
                .filter(Objects::nonNull) // 过滤掉可能在EmergencyContactes中不存在的id
                .collect(Collectors.toList());

        for (int i = 0; i < sortedProfiles.size(); i++) {
            Profile profile = sortedProfiles.get(i);
            profile.setSort(i);
            profileMapper.saveAndFlush(profile);
        }
        return 1;
    }

    @Override
    public int delById(ProfileBaseRequest profileBaseRequest) {
        profileMapper.deleteById(profileBaseRequest.getId());
        profileGroupRelMapper.deleteByProfileId(profileBaseRequest.getId());
        ProfilePrompt profilePrompt = profilePromptMapper.findByProfileId(profileBaseRequest.getId());
        // 更新档案推送
        if(Objects.nonNull(profileBaseRequest.getRegistrationId())){
            userInfoService.initPushData(Collections.singletonList(profilePrompt), ActionUnit.DELETE,null);
        }
        profilePromptMapper.deleteById(profilePrompt.getId());
        return 1;
    }

    @Override
    public List<ProfileVO> getProfileList(ProfileBaseRequest profileBaseRequest) {
        List<ProfileVO> profileVOList = new ArrayList<>();
        // 查询分组
        List<Profile> profileList = profileMapper.findAll(this.build(profileBaseRequest));

        List<Long> profileIds = new ArrayList<>();
        if(Objects.nonNull(profileBaseRequest.getGroupId())){
            List<ProfileGroupRel> profileGroupRels = profileGroupRelMapper.findByProfileGroupId(profileBaseRequest.getGroupId());
            profileIds = profileGroupRels.stream().map(ProfileGroupRel::getProfileId).collect(Collectors.toList());
        }
        profileList.sort(Comparator.comparingInt((Profile profile) ->
                        profile.getRelationship().toValue() == 0 ? -1 : // 如果Relationship为0，则返回-1以优先排序
                                profile.getSort())
                .thenComparingInt(Profile::getSort)
                .thenComparing((p1, p2) ->
                        p2.getCreateTime().compareTo(p1.getCreateTime()))); // 注意这里的reverse是通过交换比较对象实现的
        // 查询提醒数据
        List<ProfilePrompt> profilePromptList = profilePromptMapper.findByUserId(profileBaseRequest.getUserId());
        Map<Long, ProfilePrompt> profilePromptMap = profilePromptList.stream().collect(Collectors.toMap(ProfilePrompt::getProfileId, profilePrompt -> profilePrompt));
        // 查询分组数据
        List<ProfileGroupRel> profileGroupRelList = profileGroupRelMapper.findByUserId(profileBaseRequest.getUserId());
        Map<Long, List<ProfileGroupRel>> profileGroupRelListMap = profileGroupRelList.stream().collect(Collectors.groupingBy(ProfileGroupRel::getProfileId));

        // 获取用户信息以确定语言类型
        UserInfoVO userInfoVO = userInfoService.getByUserId(profileBaseRequest.getUserId());

        for (Profile profile : profileList) {
            ProfileVO profileVO = new ProfileVO();
            BeanUtils.copyProperties(profile, profileVO);
            
            // 根据用户语言类型设置生肖和星座字段
            if(Objects.nonNull(userInfoVO) && Objects.nonNull(userInfoVO.getLanguageType())) {
                if(userInfoVO.getLanguageType().equals(LanguageType.EN)) {
                    // 英文用户：星座和生肖都显示英文
                    profileVO.setZodiacSignTypeStr(StringUtils.isNotEmpty(profile.getZodiacSignType().getEnValue()) ? profile.getZodiacSignType().getEnValue() : profile.getZodiacSignType().getValue());
                    profileVO.setZodiacTypeStr(StringUtils.isNotEmpty(profile.getZodiacType().getEnValue()) ? profile.getZodiacType().getEnValue() : profile.getZodiacType().getValue());
                } else {
                    // 中文用户：正常显示
                    profileVO.setZodiacSignTypeStr(profile.getZodiacSignType().getValue());
                    profileVO.setZodiacTypeStr(profile.getZodiacType().getValue());
                }
            } else {
                // 默认中文显示
                profileVO.setZodiacSignTypeStr(profile.getZodiacSignType().getValue());
                profileVO.setZodiacTypeStr(profile.getZodiacType().getValue());
            }
            
            profileVO.setZodiacTypePic(domain+DEFAULT_ZODIAC_PIC_URL+profile.getZodiacType().toValue()+".png");
            profileVO.setZodiacSignTypePic(domain+DEFAULT_ZODIAC_SIGN_PIC_URL+profile.getZodiacSignType().toValue()+".png");
            
            // 根据用户语言类型设置关系字符串
            String relationshipStr = profile.getRelationship().getValue();
            if(Objects.nonNull(userInfoVO) && Objects.nonNull(userInfoVO.getLanguageType())) {
                if(userInfoVO.getLanguageType().equals(LanguageType.EN)) {
                    // 英文用户返回英文字段，如果英文字段为空则返回中文字段
                    relationshipStr = StringUtils.isNotEmpty(profile.getRelationship().getEnValue()) ? profile.getRelationship().getEnValue() : profile.getRelationship().getValue();
                }
            }
            profileVO.setRelationshipStr(relationshipStr);
            
            // 农历转换
            String[] lunarSpl = profile.getLunarTime().split(" ")[0].split("/");
            String[] lunarTimeSpl = profile.getLunarTime().split(" ")[1].split(":");
            LunarDate lunarDate = LunarDate.from(profile.getSolarTime());
            LunarHour lunarHour = LunarHour.fromYmdHms(Integer.parseInt(lunarSpl[0]), Integer.parseInt(lunarSpl[1]), Integer.parseInt(lunarSpl[2]), Integer.parseInt(lunarTimeSpl[0]), Integer.parseInt(lunarTimeSpl[1]), 0);
            profileVO.setLunarStr(lunarDate.getlMonthCn()+"月"+lunarDate.getlDayCn());
            profileVO.setLunarYearStr(lunarDate.getlYearCn()+"年"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"月"+lunarDate.getlDayCn());
            profileVO.setLunarFullStr(lunarDate.getlYearCn()+"年"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"月"+lunarDate.getlDayCn()+" " + lunarHour.getHour()+"点"+lunarHour.getMinute()+"分");
            
            // 根据用户语言类型设置公历日期格式
            if(Objects.nonNull(userInfoVO) && Objects.nonNull(userInfoVO.getLanguageType())) {
                if(userInfoVO.getLanguageType().equals(LanguageType.EN)) {
                    // 英文日期格式：时间(AM/PM)+月日,年(December 19,1987)
                    DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MMMM", Locale.ENGLISH);
                    DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("d", Locale.ENGLISH);
                    DateTimeFormatter yearFormatter = DateTimeFormatter.ofPattern("yyyy", Locale.ENGLISH);
                    DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("h:mm a", Locale.ENGLISH);
                    
                    String monthStr = profileVO.getSolarTime().format(monthFormatter);
                    String dayStr = profileVO.getSolarTime().format(dayFormatter);
                    String yearStr = profileVO.getSolarTime().format(yearFormatter);
                    String timeStr = profileVO.getSolarTime().format(timeFormatter);
                    
                    profileVO.setSolarStr(monthStr + " " + dayStr);
                    profileVO.setSolarYearStr(monthStr + " " + dayStr + "," + yearStr);
                    profileVO.setSolarFullStr(timeStr + " " + monthStr + " " + dayStr + "," + yearStr);
                } else {
                    // 中文日期格式
                    profileVO.setSolarStr(profileVO.getSolarTime().getMonthValue()+"月"+profileVO.getSolarTime().getDayOfMonth()+"日");
                    profileVO.setSolarYearStr(profileVO.getSolarTime().getYear()+"年"+profileVO.getSolarTime().getMonthValue()+"月"+profileVO.getSolarTime().getDayOfMonth()+"日");
                    profileVO.setSolarFullStr(profileVO.getSolarTime().getYear()+"年"+profileVO.getSolarTime().getMonthValue()+"月"+profileVO.getSolarTime().getDayOfMonth()+"日"+ " "+profileVO.getSolarTime().getHour()+"点"+ profileVO.getSolarTime().getMinute()+"分");
                }
            } else {
                // 默认中文日期格式
                profileVO.setSolarStr(profileVO.getSolarTime().getMonthValue()+"月"+profileVO.getSolarTime().getDayOfMonth()+"日");
                profileVO.setSolarYearStr(profileVO.getSolarTime().getYear()+"年"+profileVO.getSolarTime().getMonthValue()+"月"+profileVO.getSolarTime().getDayOfMonth()+"日");
                profileVO.setSolarFullStr(profileVO.getSolarTime().getYear()+"年"+profileVO.getSolarTime().getMonthValue()+"月"+profileVO.getSolarTime().getDayOfMonth()+"日"+ " "+profileVO.getSolarTime().getHour()+"点"+ profileVO.getSolarTime().getMinute()+"分");
            }
            // 计算距离生日天数
            Map<String, String> solarMap = calculateNextBirthday(profileVO.getSolarTime().toLocalDate(),LocalDate.now());
            profileVO.setSolarNextAge(Integer.parseInt(solarMap.get("age")));
            profileVO.setSolarNextAgeDay(Integer.parseInt(solarMap.get("day")));

            Map<String, String> lunarMap = calculateNextLunarBirthday(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]),LocalDate.now());
            profileVO.setLunarNextAge(Integer.parseInt(lunarMap.get("age")));
            profileVO.setLunarNextAgeDay(Integer.parseInt(lunarMap.get("day")));
            if (profileIds.contains(profileVO.getId())) {
                profileVO.setInGroup(DefaultFlag.YES);
            } else {
                profileVO.setInGroup(DefaultFlag.NO);
            }
            // 查询单个填充星座解读数据
            if(Objects.nonNull(profileBaseRequest.getId())){
                BirthdayInfo birthdayInfo = birthdayInfoMapper.findByMonthAndDay(profileVO.getSolarTime().getMonthValue(), profileVO.getSolarTime().getDayOfMonth());
                // 根据用户语言类型返回对应的字段
                String infoContent = birthdayInfo.getInfo();
                if(Objects.nonNull(userInfoVO) && Objects.nonNull(userInfoVO.getLanguageType())) {
                    if(userInfoVO.getLanguageType().equals(LanguageType.EN)) {
                        // 英文用户返回英文字段，如果英文字段为空则返回中文字段
                        infoContent = StringUtils.isNotEmpty(birthdayInfo.getInfoEn()) ? birthdayInfo.getInfoEn() : birthdayInfo.getInfo();
                    }
                }
                profileVO.setBirthdayInfo(JSONObject.parseObject(infoContent));
            }
            // 查询提醒信息
            ProfilePrompt profilePrompt = profilePromptMap.get(profileVO.getId());
            profileVO.setOnTheDay(profilePrompt.getOnTheDay());
            profileVO.setOneDayBefore(profilePrompt.getOneDayBefore());
            profileVO.setThreeDaysBefore(profilePrompt.getThreeDaysBefore());
            profileVO.setSevenDaysBefore(profilePrompt.getSevenDaysBefore());
            profileVO.setFifteenDaysBefore(profilePrompt.getFifteenDaysBefore());
            profileVO.setThirtyDaysBefore(profilePrompt.getThirtyDaysBefore());
            profileVO.setPromptTime(profilePrompt.getPromptTime());
            profileVO.setPromptFlag(profilePrompt.getPromptFlag());
            // 所属分组
            List<ProfileGroupRel> profileGroupRels = profileGroupRelListMap.get(profileVO.getId());
            if(Objects.nonNull(profileGroupRels)){
                List<String> stringList = profileGroupRels.stream().map(pg -> Long.toString(pg.getProfileGroupId())).collect(Collectors.toList());
                profileVO.setGroupIds(String.join(",", stringList));
            }else{
                profileVO.setGroupIds("");
            }

            // 填充数据
            if(Objects.nonNull(profileBaseRequest.getInGroup())){
                if(profileBaseRequest.getInGroup().equals(profileVO.getInGroup())){
                    profileVOList.add(profileVO);
                }
            }else{
                profileVOList.add(profileVO);
            }
        }
        if(Objects.isNull(profileBaseRequest.getInGroup())){
            profileVOList.sort(Comparator.comparing(ProfileServiceImpl::getSortKey));
        }else{
            profileVOList.sort(Comparator.comparing((ProfileVO p) -> p.getBirthdayType().equals(BirthdayType.SOLAR) ? p.getSolarNextAgeDay() : p.getLunarNextAgeDay()));
        }
        return profileVOList;
    }

    private static String getSortKey(ProfileVO p) {
        String name = p.getName();
        if (name == null || name.isEmpty()) {
            return "\uFFFF"; // 空或空白名称排在最后
        }
        char firstChar = name.charAt(0);


        // 如果是中文字符，则返回其拼音首字母
        if (Character.toString(firstChar).matches("[\\u4E00-\\u9FA5]")) { // 判断是否为中文字符
            try {
                String pinyin = PinyinHelper.getShortPinyin(name.substring(0,1));
                return pinyin.substring(0, 1).toLowerCase(Locale.ROOT);
            }catch (Exception e){
                e.printStackTrace();
                return "";
            }
        }

        // 如果是字母，则直接返回小写形式
        if (Character.isLetter(firstChar)) {
            return name.toLowerCase(Locale.ROOT);
        }

        // 非字母也非中文字符，排在最后
        return "\uFFFF" + name;
    }

    @Override
    public ProfileGroupResponse getGroupList(ProfileBaseRequest profileBaseRequest) {
        List<ProfileVO> profileList = getProfileList(profileBaseRequest);
        // 查询分组数据
        List<ProfileGroupVO> profileGroupVOList = new ArrayList<>();
        List<ProfileGroup> profileGroupList = profileGroupMapper.findByUserId(profileBaseRequest.getUserId());
        profileGroupList.sort(Comparator.comparingInt(ProfileGroup::getSort)
                .thenComparing(ProfileGroup::getCreateTime));
        List<ProfileGroupRel> profileGroupRels = profileGroupRelMapper.findByUserId(profileBaseRequest.getUserId());
        Map<Long, List<ProfileGroupRel>> relMap = profileGroupRels.stream().collect(Collectors.groupingBy(ProfileGroupRel::getProfileGroupId));
        
        // 获取用户信息以确定语言类型
        UserInfoVO userInfoVO = userInfoService.getByUserId(profileBaseRequest.getUserId());
        
        // 加入默认分组
        ProfileGroupVO defaultGroupVO = new ProfileGroupVO();
        List<ProfileVO> defProfiles = profileList.stream()
                .filter(profile -> profile.getRelationship().toValue() == 1)
                .collect(Collectors.toList());
        defaultGroupVO.setCount(defProfiles.size());
        
        // 根据用户语言类型设置默认分组名称
        String defaultGroupName = "默认分组";
        if(Objects.nonNull(userInfoVO) && Objects.nonNull(userInfoVO.getLanguageType())) {
            if(userInfoVO.getLanguageType().equals(LanguageType.EN)) {
                defaultGroupName = "Default";
            }else if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                defaultGroupName = "預設分組";
            }
        }
        defaultGroupVO.setGroupName(defaultGroupName);
        
        defProfiles.sort(Comparator.comparing((ProfileVO p) -> p.getBirthdayType().equals(BirthdayType.SOLAR) ? p.getSolarNextAgeDay() : p.getLunarNextAgeDay()));
        defaultGroupVO.setProfiles(defProfiles);
        profileGroupVOList.add(defaultGroupVO);

        for (ProfileGroup profileGroup : profileGroupList) {
            ProfileGroupVO profileGroupVO = new ProfileGroupVO();
            BeanUtils.copyProperties(profileGroup, profileGroupVO);
            // 查询分组档案数量
            int size = relMap.getOrDefault(profileGroup.getId(),new ArrayList<>()).size();
            profileGroupVO.setCount(size);
            // 获取该组内关系为1的档案
            List<ProfileVO> profiles = profileList.stream()
                    .filter(profile -> profile.getRelationship().toValue() == 1)
                    .filter(profile -> relMap.get(profileGroup.getId()) != null &&
                            relMap.get(profileGroup.getId()).stream()
                                    .anyMatch(rel -> rel.getProfileId().equals(profile.getId())))
                    .collect(Collectors.toList());
            profiles.sort(Comparator.comparing((ProfileVO p) -> p.getBirthdayType().equals(BirthdayType.SOLAR) ? p.getSolarNextAgeDay() : p.getLunarNextAgeDay()));
            profileGroupVO.setProfiles(profiles);
            profileGroupVOList.add(profileGroupVO);
        }
        return ProfileGroupResponse.builder()
                .selfProfile(profileList.stream()
                        .filter(profile -> profile.getRelationship().toValue() == 0)
                        .findFirst() // 获取第一个匹配的结果
                        .orElse(null))
                .profileGroups(profileGroupVOList)
                .build();
    }

    /**
     * 计算距离下一次生日的年龄和天数.
     * 对于2月29日出生者，按下一个闰年的2月29日作为生日.
     * @param birthDate 出生日期时间
     * @return 一个字符串，包含年龄和距离下一次生日的天数
     */
    public static Map<String,String> calculateNextBirthday(LocalDate birthDate,LocalDate currentDate) {
        // 计算年龄calculateNextBirthday
        int age = Period.between(birthDate, currentDate).getYears();
        if (birthDate.isBefore(currentDate)) {
            age++; // 如果今年的生日已过，则年龄增加
        }

        LocalDate nextBirthday;
        if (birthDate.getDayOfMonth() == 29 && birthDate.getMonthValue() == 2) {
            // 查找下一个闰年的2月29日作为生日
            int year = currentDate.getYear();
            while (!Year.isLeap(year + 1)) { // 寻找下一个闰年
                year++;
            }
            nextBirthday = LocalDate.of(year + 1, 2, 29); // 跳过当前年，直接到下一个闰年
        } else {
            // 对于其他日期，直接计算下一年的相同月份和日期
            LocalDate thisYearsBirthday = currentDate.withMonth(birthDate.getMonthValue()).withDayOfMonth(birthDate.getDayOfMonth());
            if (currentDate.isBefore(thisYearsBirthday) || currentDate.isEqual(thisYearsBirthday)) {
                nextBirthday = thisYearsBirthday;
            } else {
                nextBirthday = thisYearsBirthday.plusYears(1);
            }
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 计算距离下次生日的天数
        long daysUntilNextBirthday = ChronoUnit.DAYS.between(currentDate, nextBirthday);
        Map<String,String> map = new LinkedHashMap<>();
        map.put("age", String.valueOf(age));
        map.put("day", String.valueOf(daysUntilNextBirthday));
        map.put("nextBirthday",nextBirthday.format(formatter));
//        System.out.println("距离下一次生日: " + age + "岁，" + daysUntilNextBirthday + "天");
        return map;
    }


    public static Map<String,String> calculateNextLunarBirthday(int lunarYear, int lunarMonth, int lunarDay,LocalDate today) {

        // 创建农历生日日期对象
        LunarDate lunarBirthday = LunarDate.of(lunarYear, lunarMonth, lunarDay);

        // 获取当前日期对应的农历日期
        LunarDate todayLunar = LunarDate.from(today);

        // 确定农历生日的年份
        int nextYear = today.getYear();
        LocalDate date = LunarDate.of(nextYear, lunarMonth, lunarDay).getLocalDate();
        int newYear = (nextYear-1);
        LocalDate lastDate = LunarDate.of(newYear, lunarMonth, lunarDay).getLocalDate();
        if(today.isBefore(lastDate) || today.equals(lastDate)){
            nextYear = newYear;
        }
        if (!todayLunar.getLocalDate().isBefore(date) && !todayLunar.getLocalDate().equals(date)) {
            // 如果当前的农历日期已经超过了生日日期，则计算下一年的生日
            nextYear += 1;
        }

        // 创建下一次农历生日日期对象
        LunarDate nextLunarBirthday = findValidLunarBirthday(nextYear, lunarMonth, lunarDay);

        // 将农历生日转换为公历日期
        LocalDate nextSolarDate = nextLunarBirthday.getLocalDate();

        // 计算年龄
        int age = Period.between(lunarBirthday.getLocalDate(), nextSolarDate).getYears();
        if (todayLunar.getLocalDate().isBefore(nextLunarBirthday.getLocalDate())) {
            age++; // 如果今年的生日已过，则年龄增加
        }

        // 计算距离下一个农历生日的天数
        long daysUntilNextBirthday = ChronoUnit.DAYS.between(today, nextSolarDate);
        Map<String,String> map = new LinkedHashMap<>();
        map.put("age", String.valueOf(age));
        map.put("day", String.valueOf( daysUntilNextBirthday));
        map.put("nextBirthday",nextYear + "/" + lunarMonth + "/" + lunarDay);
        return map;
    }

    private static LunarDate findValidLunarBirthday(int year, int month, int day) {
        LunarDate nextLunarBirthday = null;
        while (nextLunarBirthday == null) {
            try {
                nextLunarBirthday = LunarDate.of(year, month, day);
            } catch (Exception e) {
                year++;
            }
        }
        return nextLunarBirthday;
    }
    private Specification<Profile> build(ProfileBaseRequest profileBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // id
            if(profileBaseRequest.getId()!=null) {
                predicates.add(cbuild.equal(root.get("Id"), profileBaseRequest.getId()));
            }
            // 用户id
            if(profileBaseRequest.getUserId()!=null) {
                predicates.add(cbuild.equal(root.get("userId"), profileBaseRequest.getUserId()));
            }
            // 性别
            if(profileBaseRequest.getGender()!=null){
                predicates.add(cbuild.equal(root.get("gender"), profileBaseRequest.getGender()));
            }
            if(profileBaseRequest.getRelationship()!=null){
                predicates.add(cbuild.equal(root.get("relationship"), profileBaseRequest.getRelationship()));
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }

}
