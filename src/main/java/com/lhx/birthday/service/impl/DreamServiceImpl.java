package com.lhx.birthday.service.impl;

import com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap;
import com.lhx.birthday.entity.Dream;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.enums.ZhouGongSecType;
import com.lhx.birthday.enums.ZhouGongType;
import com.lhx.birthday.mapper.DreamMapper;
import com.lhx.birthday.request.dream.DreamBaseRequest;
import com.lhx.birthday.request.dream.DreamDetailRequest;
import com.lhx.birthday.request.dream.DreamRequest;
import com.lhx.birthday.response.dream.DreamDetailResponse;
import com.lhx.birthday.response.dream.DreamHotResponse;
import com.lhx.birthday.response.dream.DreamResponse;
import com.lhx.birthday.service.IDreamService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import com.lhx.birthday.util.XssUtils;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.dream.DreamDetailVO;
import com.lhx.birthday.vo.dream.DreamHotVO;
import com.lhx.birthday.vo.dream.DreamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DreamServiceImpl implements IDreamService {

    @Autowired
    private DreamMapper dreamMapper;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private CommonUtil commonUtil;

    @Override
    public DreamHotResponse dreamHotList() {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        DreamBaseRequest dreamBaseRequest = new DreamBaseRequest();
        dreamBaseRequest.setIsShow(1);
        dreamBaseRequest.setSortKey("sort");
        dreamBaseRequest.setSortMethod("asc");
        dreamBaseRequest.setPageSize(2000);
        Page<Dream> dreamPage = dreamMapper.findAll(this.build(dreamBaseRequest), dreamBaseRequest.getPageInfo());
        List<DreamHotVO> dreamHotVOS = new ArrayList<>();
        dreamPage.get().forEach(dream -> {
            String title = dream.getKeyword();
            // 根据用户语言类型返回对应字段
            if(Objects.nonNull(userInfoVO)) {
                if(userInfoVO.getLanguageType().equals(LanguageType.EN)) {
                    // 英文用户返回英文字段，如果英文字段为空则返回中文字段
                    title = StringUtils.isNotEmpty(dream.getKeywordEn()) ? dream.getKeywordEn() : dream.getKeyword();
                } else if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)) {
                    // 繁体中文用户转换为繁体
                    ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
                    title = zhConvertBootstrap.toTraditional(title);
                }
            }
            dreamHotVOS.add(DreamHotVO.builder()
                    .title(title)
                    .id(dream.getId())
                    .build());
        });
        return DreamHotResponse.builder()
                .dreamHots(dreamHotVOS)
                .build();
    }

    @Override
    public DreamResponse search(DreamRequest dreamRequest) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        dreamRequest.setSortKey("type");
        dreamRequest.setSortMethod("asc");
        DreamBaseRequest dreamBaseRequest = new DreamBaseRequest();
        BeanUtils.copyProperties(dreamRequest, dreamBaseRequest);
        // 传递用户语言类型到搜索条件中
        dreamBaseRequest.setLanguageType(userInfoVO != null ? userInfoVO.getLanguageType() : null);
        Page<Dream> dreamPage = dreamMapper.findAll(this.build(dreamBaseRequest), dreamBaseRequest.getPageInfo());
        Page<DreamVO> result = new PageImpl<>(dreamPage.getContent().stream().map(dream -> {
            String title = dream.getTitle();
            // 根据用户语言类型返回对应字段
            if(Objects.nonNull(userInfoVO)) {
                if(userInfoVO.getLanguageType().equals(LanguageType.EN)) {
                    // 英文用户返回英文字段，如果英文字段为空则返回中文字段
                    title = StringUtils.isNotEmpty(dream.getTitleEn()) ? dream.getTitleEn() : dream.getTitle();
                } else if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)) {
                    // 繁体中文用户转换为繁体
                    ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
                    title = zhConvertBootstrap.toTraditional(title);
                }
            }
            DreamVO dreamVO = DreamVO.builder()
                    .id(dream.getId())
                    .title(title)
                    .build();
            return dreamVO;
        }).collect(Collectors.toList()), dreamPage.getPageable(), dreamPage.getTotalElements());
        return DreamResponse.builder()
                .dreams(result)
                .build();
    }

    @Override
    public DreamDetailResponse detail(DreamDetailRequest dreamDetailRequest) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        Optional<Dream> optionalDream = dreamMapper.findById(dreamDetailRequest.getId());
        Dream dream = optionalDream.get();
        String title = dream.getTitle();
        String type = ZhouGongType.fromValue(dream.getType()).getValue();
        String info = dream.getInfo();
        String scType = ZhouGongSecType.fromValue(dream.getSecType()).getValue();

        // 根据用户语言类型返回对应字段
        if(Objects.nonNull(userInfoVO)) {
            if(userInfoVO.getLanguageType().equals(LanguageType.EN)) {
                // 英文用户返回英文字段，如果英文字段为空则返回中文字段
                title = StringUtils.isNotEmpty(dream.getTitleEn()) ? dream.getTitleEn() : dream.getTitle();
                // 如果翻译状态为 partial，表示翻译有问题，返回中文内容
                if("partial".equals(dream.getTranslationStatus())) {
                    info = dream.getInfo();
                } else {
                    info = StringUtils.isNotEmpty(dream.getInfoEn()) ? dream.getInfoEn() : dream.getInfo();
                }
                // 注意：这里的type和scType是枚举值，暂时保持中文，如果需要英文版本需要扩展枚举类
            } else if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)) {
                // 繁体中文用户转换为繁体
                ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
                title = zhConvertBootstrap.toTraditional(title);
                type = zhConvertBootstrap.toTraditional(type);
                info = zhConvertBootstrap.toTraditional(info);
                if(dream.getSecType()!=-1){
                    scType = zhConvertBootstrap.toTraditional(scType);
                }
            }
        }

        DreamDetailVO dreamDetailVO = DreamDetailVO.builder()
                .title(title)
                .type(type)
                .secType("")
                .content(info)
                .build();
        if(dream.getSecType()!=-1){
            dreamDetailVO.setSecType(scType);
        }
        return DreamDetailResponse.builder()
                .dreamDetail(dreamDetailVO)
                .build();
    }

    private Specification<Dream> build(DreamBaseRequest dreamBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // id
            if(dreamBaseRequest.getId()!=null) {
                predicates.add(cbuild.equal(root.get("id"), dreamBaseRequest.getId()));
            }
            // 热门梦境
            if(dreamBaseRequest.getIsShow()!=null) {
                predicates.add(cbuild.equal(root.get("isShow"), dreamBaseRequest.getIsShow()));
            }
            // 关键字搜索 - 根据用户语言类型搜索对应字段
            if(dreamBaseRequest.getKeyword()!=null){
                String keyword = XssUtils.replaceLikeWildcard(dreamBaseRequest.getKeyword());
                String likePattern = StringUtil.SQL_LIKE_CHAR.concat(keyword).concat(StringUtil.SQL_LIKE_CHAR);

                if(dreamBaseRequest.getLanguageType() != null && dreamBaseRequest.getLanguageType().equals(LanguageType.EN)) {
                    // 英文用户：优先搜索英文字段，同时也搜索中文字段作为备选
                    Predicate titleEnPredicate = cbuild.like(root.get("titleEn"), likePattern);
                    Predicate titlePredicate = cbuild.like(root.get("title"), likePattern);
                    predicates.add(cbuild.or(titleEnPredicate, titlePredicate));
                } else {
                    // 中文用户（简体或繁体）：只搜索中文字段
                    predicates.add(cbuild.like(root.get("title"), likePattern));
                }
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }

}
