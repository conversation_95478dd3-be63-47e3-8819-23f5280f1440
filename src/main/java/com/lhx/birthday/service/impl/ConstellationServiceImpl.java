package com.lhx.birthday.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.entity.ConstellationProfile;
import com.lhx.birthday.entity.CountryCode;
import com.lhx.birthday.entity.Profile;
import com.lhx.birthday.entity.ProfilePrompt;
import com.lhx.birthday.enums.ActionUnit;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.mapper.ConstellationMapper;
import com.lhx.birthday.mapper.CountryCodeMapper;
import com.lhx.birthday.request.constellation.ConstellationBaseRequest;
import com.lhx.birthday.request.constellation.ConstellationProfileUpdateSortRequest;
import com.lhx.birthday.request.profile.ProfileUpdateSortRequest;
import com.lhx.birthday.service.IAstrolabeService;
import com.lhx.birthday.service.IConstellationService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.vo.constellation.ConstellationProfileVO;
import com.lhx.birthday.vo.constellation.CountryCodeVO;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.systemConfig.SystemConfigVO;
import com.tyme.culture.Constellation;
import com.tyme.lunar.LunarDay;
import com.tyme.solar.SolarDay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.lhx.birthday.util.StringUtils;

import static com.lhx.birthday.constant.SettingConstant.DEFAULT_ZODIAC_SIGN_PIC_URL;
import static com.lhx.birthday.constant.SystemConfigConstant.*;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.constant.RedisKeyConstant;

@Slf4j
@Service
public class ConstellationServiceImpl implements IConstellationService {

    @Autowired
    private ConstellationMapper constellationMapper;

    @Autowired
    private SystemConfigServiceImpl systemConfigService;

    @Autowired
    private CountryCodeMapper countryCodeMapper;

    @Autowired
    private IAstrolabeService astrolabeService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IUserInfoService userInfoService;

    @Value("${oss.staticDomain}")
    private String domain;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addConstellation(ConstellationBaseRequest request) {
        ConstellationProfile addConstellationProfile = new ConstellationProfile();
        BeanUtils.copyProperties(request, addConstellationProfile);
        addConstellationProfile.setSort(0);
        addConstellationProfile.setCreateTime(LocalDateTime.now());
        addConstellationProfile.setModifyTime(LocalDateTime.now());

        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_DEFAULT_PLACE);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());

        // 如果出生地点和居住地点为空，使用默认配置
        if (request.getBirthPlaceId() == null) {
            addConstellationProfile.setBirthPlaceId(jsonObject.getString("birthPlaceId"));
        }
        if (request.getLivingPlaceId() == null) {
            addConstellationProfile.setLivingPlaceId(jsonObject.getString("livingPlaceId"));
        }

        // 计算生肖星座
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime birthdayTime = LocalDateTime.parse(request.getBirthdayTime(), formatter);
        SolarDay solarDay = SolarDay.fromYmd(birthdayTime.getYear(), birthdayTime.getMonthValue(), birthdayTime.getDayOfMonth());
        LunarDay lunarDay = solarDay.getLunarDay();
        Constellation constellation = lunarDay.getSolarDay().getConstellation();
        addConstellationProfile.setZodiacSignType(ZodiacSignType.values()[constellation.getIndex()]);
        ConstellationProfile savedProfile = constellationMapper.saveAndFlush(addConstellationProfile);

        // 调用本命盘接口获取数据
        astrolabeService.getNatalChart(savedProfile.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateConstellation(ConstellationBaseRequest request) {
        Optional<ConstellationProfile> constellationProfileOptional = constellationMapper.findById(request.getId());
        if(constellationProfileOptional.isPresent()){
            ConstellationProfile constellationProfile = constellationProfileOptional.get();
            constellationProfile.setModifyTime(LocalDateTime.now());
            constellationProfile.setName(request.getName());
            constellationProfile.setGender(request.getGender());
            constellationProfile.setBirthdayTime(request.getBirthdayTime());
            constellationProfile.setBirthdayType(request.getBirthdayType());
            constellationProfile.setRelationship(request.getRelationship());
            constellationProfile.setTimeZone(request.getTimeZone());

            // 处理出生地点和居住地点
            SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_DEFAULT_PLACE);
            JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
            
            if (request.getBirthPlaceId() != null) {
                constellationProfile.setBirthPlaceId(request.getBirthPlaceId());
            } else {
                constellationProfile.setBirthPlaceId(jsonObject.getString("birthPlaceId"));
            }
            
            if (request.getLivingPlaceId() != null) {
                constellationProfile.setLivingPlaceId(request.getLivingPlaceId());
            } else {
                constellationProfile.setLivingPlaceId(jsonObject.getString("livingPlaceId"));
            }

            // 重新计算星座
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime birthdayTime = LocalDateTime.parse(request.getBirthdayTime(), formatter);
            SolarDay solarDay = SolarDay.fromYmd(birthdayTime.getYear(), birthdayTime.getMonthValue(), birthdayTime.getDayOfMonth());
            LunarDay lunarDay = solarDay.getLunarDay();
            Constellation constellation = lunarDay.getSolarDay().getConstellation();
            constellationProfile.setZodiacSignType(ZodiacSignType.values()[constellation.getIndex()]);
            
            ConstellationProfile savedProfile = constellationMapper.saveAndFlush(constellationProfile);

            // 删除Redis缓存
            String redisKey = RedisKeyConstant.XINGPAN_CHART_DATA_KEY + savedProfile.getId();
            redisService.delete(redisKey);

            // 调用本命盘接口获取数据
            astrolabeService.getNatalChart(savedProfile.getId());
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delById(ConstellationBaseRequest request) {
        // 删除Redis缓存
        String redisKey = RedisKeyConstant.XINGPAN_CHART_DATA_KEY + request.getId();
        redisService.delete(redisKey);
        
        constellationMapper.deleteById(request.getId());
        return 1;
    }

    @Override
    public List<ConstellationProfileVO> getConstellationList(ConstellationBaseRequest request) {
        List<ConstellationProfileVO> constellationVOList = new ArrayList<>();
        Sort sort = Sort.by(Sort.Order.asc("sort"));
        List<ConstellationProfile> constellationProfileList = constellationMapper.findAll(this.build(request), sort);

        // 收集所有的birthPlaceId和livingPlaceId
        Set<Long> placeIds = constellationProfileList.stream()
                .flatMap(profile -> Stream.of(profile.getBirthPlaceId(), profile.getLivingPlaceId()))
                .filter(Objects::nonNull)
                .map(Long::parseLong)
                .collect(Collectors.toSet());

        // 批量查询CountryCode
        Map<String, CountryCode> countryCodeMap = new HashMap<>();
        if (!placeIds.isEmpty()) {
            List<CountryCode> countryCodes = countryCodeMapper.findAllById(placeIds);
            countryCodeMap = countryCodes.stream()
                    .collect(Collectors.toMap(code -> code.getId().toString(), code -> code));
        }

        // 转换为VO对象
        for (ConstellationProfile constellationProfile : constellationProfileList) {
            ConstellationProfileVO constellationVO = new ConstellationProfileVO();
            BeanUtils.copyProperties(constellationProfile, constellationVO);

            constellationVO.setZodiacSignTypeStr(constellationProfile.getZodiacSignType().getValue());
            constellationVO.setZodiacSignTypePic(domain+DEFAULT_ZODIAC_SIGN_PIC_URL+constellationProfile.getZodiacSignType().toValue()+".png");
            // 根据用户语言类型设置关系字符串
            String relationshipStr = constellationProfile.getRelationship().getValue();
            // 获取用户信息以确定语言类型
            UserInfoVO userInfoVO = userInfoService.getByUserId(request.getUserId());
            if(Objects.nonNull(userInfoVO) && Objects.nonNull(userInfoVO.getLanguageType())) {
                if(userInfoVO.getLanguageType().equals(LanguageType.EN)) {
                    // 英文用户返回英文字段，如果英文字段为空则返回中文字段
                    relationshipStr = StringUtils.isNotEmpty(constellationProfile.getRelationship().getEnValue()) ? constellationProfile.getRelationship().getEnValue() : constellationProfile.getRelationship().getValue();
                }
            }
            constellationVO.setRelationshipStr(relationshipStr);
            // 设置birthPlace
            if (constellationProfile.getBirthPlaceId() != null) {
                CountryCode birthPlace = countryCodeMap.get(constellationProfile.getBirthPlaceId());
                if (birthPlace != null) {
                    CountryCodeVO birthPlaceVO = new CountryCodeVO();
                    BeanUtils.copyProperties(birthPlace, birthPlaceVO);
                    constellationVO.setBirthPlace(birthPlaceVO);
                }
            }

            // 设置livingPlace
            if (constellationProfile.getLivingPlaceId() != null) {
                CountryCode livingPlace = countryCodeMap.get(constellationProfile.getLivingPlaceId());
                if (livingPlace != null) {
                    CountryCodeVO livingPlaceVO = new CountryCodeVO();
                    BeanUtils.copyProperties(livingPlace, livingPlaceVO);
                    constellationVO.setLivingPlace(livingPlaceVO);
                }
            }

            constellationVOList.add(constellationVO);
        }
        return constellationVOList;
    }

    @Override
    public int updateSort(ConstellationProfileUpdateSortRequest constellationProfileUpdateSortRequest) {
        String idsStr = constellationProfileUpdateSortRequest.getIds();
        List<Long> idOrderList = Arrays.stream(idsStr.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<ConstellationProfile> constellationProfiles = constellationMapper.findAllById(idOrderList);

        Map<Long, ConstellationProfile> constellationProfileMap = new LinkedHashMap<>();
        for (ConstellationProfile constellationProfile : constellationProfiles) {
            constellationProfileMap.put(constellationProfile.getId(), constellationProfile);
        }

        List<ConstellationProfile> sortedProfiles = idOrderList.stream()
                .map(constellationProfileMap::get)
                .filter(Objects::nonNull) // 过滤掉可能在EmergencyContactes中不存在的id
                .collect(Collectors.toList());

        for (int i = 0; i < sortedProfiles.size(); i++) {
            ConstellationProfile constellationProfile = sortedProfiles.get(i);
            constellationProfile.setSort(i);
            constellationMapper.saveAndFlush(constellationProfile);
        }
        return 1;
    }

    private Specification<ConstellationProfile> build(ConstellationBaseRequest constellationBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // id
            if(constellationBaseRequest.getId()!=null) {
                predicates.add(cbuild.equal(root.get("id"), constellationBaseRequest.getId()));
            }
            // 用户id
            if(constellationBaseRequest.getUserId()!=null) {
                predicates.add(cbuild.equal(root.get("userId"), constellationBaseRequest.getUserId()));
            }
            // 性别
            if(constellationBaseRequest.getGender()!=null){
                predicates.add(cbuild.equal(root.get("gender"), constellationBaseRequest.getGender()));
            }
            if(constellationBaseRequest.getRelationship()!=null){
                predicates.add(cbuild.equal(root.get("relationship"), constellationBaseRequest.getRelationship()));
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }
} 