package com.lhx.birthday.entity;

import com.lhx.birthday.enums.CycleUnit;
import com.lhx.birthday.enums.DefaultFlag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/11/1 11:01
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "tv_member_recharge_type")
public class TvMemberRechargeType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    private LocalDateTime createDate;

    /**
     * 修改日期
     */
    @Column(name = "modify_date")
    private LocalDateTime modifyDate;

    /**
     * 排序
     */
    @Column(name = "orders")
    private Integer orders;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    @Column(name = "name_en")
    private String nameEn;

    /**
     * 编码
     */
    @Column(name = "code")
    private String code;

    /**
     * 有效期（天）
     */
    @Column(name = "expire")
    private Integer expire;

    /**
     * iOS价格
     */
    @Column(name = "price_ios")
    private BigDecimal priceIos;

    /**
     * Android价格
     */
    @Column(name = "price_android")
    private BigDecimal priceAndroid;

    /**
     * 是否自动订阅
     */
    @Column(name = "auto_subscibe")
    private Boolean autoSubscribe;

    /**
     * 是否在iOS端展示
     */
    @Column(name = "ios_show")
    private Boolean iosShow;

    /**
     * 是否在Android端展示
     */
    @Column(name = "android_show")
    private Boolean androidShow;

    /**
     * 是否有折扣
     */
    @Column(name = "discount")
    private Boolean discount;

    /**
     * 折扣开始时间
     */
    @Column(name = "discount_begin")
    private LocalDateTime discountBegin;

    /**
     * 折扣结束时间
     */
    @Column(name = "discount_end")
    private LocalDateTime discountEnd;

    /**
     * 折扣码
     */
    @Column(name = "code_discount")
    private String codeDiscount;

    /**
     * 折扣后iOS价格
     */
    @Column(name = "price_discount_ios")
    private BigDecimal priceDiscountIos;

    /**
     * 折扣后Android价格
     */
    @Column(name = "price_discount_android")
    private BigDecimal priceDiscountAndroid;

    /**
     * 最大折扣额度
     */
    @Column(name = "discount_max")
    private Integer discountMax;

    @Enumerated
    @Column(name = "cycle_unit")
    private CycleUnit cycleUnit;

    @Column(name = "apple_free_day")
    private Integer appleFreeDay;

    @Column(name = "user_center")
    private DefaultFlag userCenter;

    public TvMemberRechargeType() {

    }
}
