package com.lhx.birthday.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 性格类型实体
 * <AUTHOR> lhx
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "personality")
public class Personality implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "type_code")
    private String typeCode;

    @Column(name = "type_name")
    private String typeName;

    @Column(name = "type_name_en")
    private String typeNameEn;

    @Column(name = "type_title")
    private String typeTitle;

    @Column(name = "type_title_en")
    private String typeTitleEn;

    @Column(name = "type_desc")
    private String typeDesc;

    @Column(name = "type_desc_en")
    private String typeDescEn;

    @Column(name = "tag_name")
    private String tagName;

    @Column(name = "tag_name_en")
    private String tagNameEn;

    @Column(name = "description")
    private String description;

    @Column(name = "description_en")
    private String descriptionEn;
} 