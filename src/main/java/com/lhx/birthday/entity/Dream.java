package com.lhx.birthday.entity;

import com.lhx.birthday.enums.DefaultFlag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "dream")
public class Dream implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long Id;

    private String title;

    private String titleEn;

    private String keyword;

    private String keywordEn;

    private int type;

    private int secType;

    private int sort;

    @Enumerated
    private DefaultFlag isShow;

    private String info;

    private String infoEn;

    private String translationStatus;

    public Dream() {

    }
}
