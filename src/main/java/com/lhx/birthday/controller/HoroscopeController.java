package com.lhx.birthday.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.request.constellation.ConstellationBaseRequest;
import com.lhx.birthday.request.horoscope.*;
import com.lhx.birthday.request.profile.ProfileBaseRequest;
import com.lhx.birthday.service.*;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.util.RandomNumberGenerator;
import com.lhx.birthday.util.UniqueRandomNumbers;
import com.lhx.birthday.util.converUtil.ConverChinese;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.constellation.ConstellationProfileVO;
import com.lhx.birthday.vo.profile.ProfileVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:13
 */
@RestController
@Slf4j
@Api(description = "app接口 - 占卜模块", tags = "HoroscopeController")
public class HoroscopeController {

    @Autowired
    private CommonUtil commonUtil;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private IZodiacSignsService zodiacSignsService;

    @Autowired
    private IConstellationService constellationService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IHoroscopeService horoscopeService;

    @Autowired
    private IToolService toolService;

    /**
     * 十二-星座/生肖每日运势
     * @param shierYunshiRequest
     * @return
     */
    @ApiOperation("十二-星座/生肖每日运势")
    @PostMapping("/shier/yunshi")
    public Result yunshi(@RequestBody ShierYunshiRequest shierYunshiRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        // 根据语言类型返回中文/繁体/英文
        String infoByTimeAndType;
        if (userInfoVO.getLanguageType().equals(LanguageType.EN)) {
            infoByTimeAndType = zodiacSignsService.getInfoByTimeAndTypeEn(shierYunshiRequest);
            JSONArray enData = JSONArray.parseArray(infoByTimeAndType);
            return Result.ok(enData);
        } else {
            infoByTimeAndType = zodiacSignsService.getInfoByTimeAndType(shierYunshiRequest);
            JSONArray incomeData = JSONArray.parseArray(infoByTimeAndType);
            if (userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)) {
                JSONArray newArr = new JSONArray();
                for (int i = 0; i < incomeData.size(); i++) {
                    JSONObject jsonObject = incomeData.getJSONObject(i);
                    jsonObject = ConverChinese.convertValuesToTraditionalChinese(jsonObject.toJSONString());
                    newArr.add(jsonObject);
                }
                return Result.ok(newArr);
            } else {
                return Result.ok(incomeData);
            }
        }
    }

    /**
     * 占卜-星座/生肖每日运势
     * @return
     */
    @ApiOperation("占卜-星座/生肖运势")
    @PostMapping("/zhanbu/yunshi")
    public Result yunshi(@RequestBody YunshiRequest yunshiRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        String key = "";
        if(Objects.isNull(yunshiRequest.getProFileId())){
            key = RedisKeyConstant.HOROSCOPE_KEY + yunshiRequest.getType() + ":" + yunshiRequest.getTitleYunshi();
        }else{
            ConstellationBaseRequest request = new ConstellationBaseRequest();
            request.setUserId(Long.parseLong(customerId));
            List<ConstellationProfileVO> profileList = constellationService.getConstellationList(request);
            for (ConstellationProfileVO profileVO : profileList) {
                if(profileVO.getId().equals(yunshiRequest.getProFileId())){
                    if(yunshiRequest.getType()==0){
                        key = RedisKeyConstant.HOROSCOPE_KEY + yunshiRequest.getType() + ":" + profileVO.getZodiacSignType().toValue();
                    }
                }
            }
        }
        try {
            boolean isEn = userInfoVO.getLanguageType().equals(LanguageType.EN);
            String langSuffix = isEn ? ":en-us" : "";
            JSONObject jsonObject = JSONObject.parseObject(redisService.getString(key + langSuffix));
            // 获取运势排名信息
            String typeStr = jsonObject.getString("运势类型");
            String infoKey = RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.DAY_KEY + "rankInfo:" + yunshiRequest.getType() + (isEn ? ":en-us" : "");
            JSONObject infoObject = JSONObject.parseObject(redisService.getString(infoKey));
            JSONObject rankInfoObj = infoObject.getJSONObject(typeStr);
            jsonObject.put("排名",rankInfoObj);
            if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                jsonObject = ConverChinese.convertValuesToTraditionalChinese(jsonObject.toJSONString());
            }
            return Result.ok(jsonObject);
        } catch (Exception e) {
            return Result.error("error");
        }
    }

    /**
     * 八字-八字精算
     * @param jingsuanRequest
     * @return
     */
    @ApiOperation("八字-八字精算")
    @PostMapping("/bazi/jingsuan")
    public Result jingpan(@RequestBody JingsuanRequest jingsuanRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        BaziBaseRequest baziBaseRequest = new BaziBaseRequest();
        baziBaseRequest.setProFileId(jingsuanRequest.getProFileId());
        baziBaseRequest.setSect(jingsuanRequest.getSect());
        baziBaseRequest.setZhen(jingsuanRequest.getZhen());
        baziBaseRequest.setProvince(jingsuanRequest.getProvince());
        baziBaseRequest.setCity(jingsuanRequest.getCity());
        baziBaseRequest.setLanguageType(userInfoVO.getLanguageType());
        return horoscopeService.JingsuanHoroscope(baziBaseRequest);
    }

    /**
     * 占卜-一张牌占卜法
     * @return
     */
    @ApiOperation("占卜-一张牌占卜法")
    @PostMapping("/zhanbu/taluozhanbu")
    public Result taluozhanbu(){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        String key = RedisKeyConstant.DIVINATION_KEY + RandomNumberGenerator.generateRandomNumber(0, 1) + ":" + RandomNumberGenerator.generateRandomNumber(1, 22);
        if(userInfoVO.getLanguageType().equals(LanguageType.EN)){
            key = RedisKeyConstant.DIVINATION_KEY +"en:" + RandomNumberGenerator.generateRandomNumber(0, 1) + ":" + RandomNumberGenerator.generateRandomNumber(1, 22);
        }
        try {
            JSONObject resultObj;
            if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                resultObj = ConverChinese.convertValuesToTraditionalChinese(redisService.getString(key));
            }else{
                resultObj = JSONObject.parseObject(redisService.getString(key));
            }
            return Result.ok(resultObj);
        } catch (Exception e) {
            return Result.error("error");
        }
    }

    @ApiOperation("占卜-三张牌占卜法")
    @PostMapping("/zhanbu/taluozhanbu/all")
    public Result taluozhanbuAll(){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        try {
            JSONArray jsonArray = new JSONArray();
            String uniqueRandomString = UniqueRandomNumbers.generateUniqueRandomString();
            String[] ids = uniqueRandomString.split(",");
            for (int i = 0; i < 3; i++) {
                String key = RedisKeyConstant.DIVINATION_KEY + RandomNumberGenerator.generateRandomNumber(0, 1) + ":" + ids[i];
                if(userInfoVO.getLanguageType().equals(LanguageType.EN)){
                    key = RedisKeyConstant.DIVINATION_KEY +"en:" + RandomNumberGenerator.generateRandomNumber(0, 1) + ":" + ids[i];
                }
                JSONObject keyInfoObj = JSONObject.parseObject(redisService.getString(key));
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("position","第"+(i+1)+"号位牌信息");
                jsonObject.put("image",keyInfoObj.getString("image"));
                JSONObject cardInfo = new JSONObject();
                cardInfo.put("cart_reverse",keyInfoObj.getString("正逆"));
                JSONObject cardDescriptionJSONObject = new JSONObject();
                cardDescriptionJSONObject.put("base_desc",keyInfoObj.getJSONObject("含义").getString("基本含义"));
                cardDescriptionJSONObject.put("love_marriage",keyInfoObj.getJSONObject("含义").getString("恋爱婚姻"));
                cardDescriptionJSONObject.put("work_study",keyInfoObj.getJSONObject("含义").getString("工作学业"));
                cardDescriptionJSONObject.put("inter_wealth",keyInfoObj.getJSONObject("含义").getString("人际财富"));
                cardDescriptionJSONObject.put("health_life",keyInfoObj.getJSONObject("含义").getString("健康生活"));
                cardDescriptionJSONObject.put("other",keyInfoObj.getJSONObject("含义").getString("其它"));
                cardInfo.put("card_description",cardDescriptionJSONObject);
                cardInfo.put("card_name",keyInfoObj.getString("牌名"));
                cardInfo.put("card_keyword",keyInfoObj.getString("关键字"));
                cardInfo.put("card_astrology",keyInfoObj.getString("星相"));
                cardInfo.put("card_elements",keyInfoObj.getString("四要素"));
                cardInfo.put("card_summarize",keyInfoObj.getString("牌面描述"));
                jsonObject.put("card_info",cardInfo);
                if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                    jsonObject = ConverChinese.convertValuesToTraditionalChinese(jsonObject.toJSONString());
                }
                jsonArray.add(jsonObject);
            }
            return Result.ok(jsonArray);
        } catch (Exception e) {
            return Result.error("error");
        }
    }

    /**
     * 日签信息查询
     * 根据用户ID和日期存入Redis，每日一变
     * @return
     */
    @ApiOperation("日签信息查询")
    @PostMapping("/daily/sign")
    public Result dailySign() {
        // 获取用户ID
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if (Objects.isNull(userInfoVO)) {
            return Result.error("error");
        }
        
        // 获取当前日期，格式为yyyyMMdd
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 构建Redis键: dailySign:userId:date
        String redisKey = "dailySign:" + customerId + ":" + currentDate;
        
        try {
            // 检查Redis中是否已存在该用户当天的日签
            String dailySignData = redisService.getString(redisKey);
            
            // 如果不存在，则生成一个新的日签并存入Redis
            if (dailySignData == null || dailySignData.isEmpty()) {
                // 随机生成一个新的日签，使用与taluozhanbu相同的逻辑
                String divinationKey = RedisKeyConstant.DIVINATION_KEY + RandomNumberGenerator.generateRandomNumber(0, 1) + ":" + RandomNumberGenerator.generateRandomNumber(1, 22);
                if(userInfoVO.getLanguageType().equals(LanguageType.EN)){
                    divinationKey = RedisKeyConstant.DIVINATION_KEY +"en:" + RandomNumberGenerator.generateRandomNumber(0, 1) + ":" + RandomNumberGenerator.generateRandomNumber(1, 22);
                }
                dailySignData = redisService.getString(divinationKey);
                
                // 将日签数据存入Redis，设置过期时间为第二天凌晨
                LocalDate tomorrow = LocalDate.now().plusDays(1);
                LocalDate expiryDate = LocalDate.of(tomorrow.getYear(), tomorrow.getMonth(), tomorrow.getDayOfMonth());
                long expirySeconds = java.time.Duration.between(LocalDate.now().atStartOfDay(), expiryDate.atStartOfDay()).getSeconds();
                
                redisService.setString(redisKey, dailySignData, (int) expirySeconds);
            }
            
            // 返回日签数据
            JSONObject resultObj;
            if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                resultObj = ConverChinese.convertValuesToTraditionalChinese(dailySignData);
            }else{
                resultObj = JSONObject.parseObject(dailySignData);
            }
            return Result.ok(resultObj);
        } catch (Exception e) {
            log.error("获取日签信息失败", e);
            return Result.error("获取日签信息失败");
        }
    }

    /**
     * 每日锦囊
     * @return
     */
    @ApiOperation("每日锦囊")
    @PostMapping("/answer/tips")
    public Result tips(){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        JSONObject source = zodiacSignsService.getAnswer();
        JSONObject result = new JSONObject();
        if (userInfoVO.getLanguageType().equals(LanguageType.EN)) {
            String en = source.getString("answer_en");
            if (en == null || en.isEmpty()) {
                en = source.getString("answer");
            }
            result.put("answer", en);
            return Result.ok(result);
        }
        // 中文及繁体默认取中文字段
        result.put("answer", source.getString("answer"));
        if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
            result = ConverChinese.convertValuesToTraditionalChinese(result.toJSONString());
        }
        return Result.ok(result);
    }

    /**
     * 八字-紫微排盘
     * @param weilaiRequest
     * @return
     */
    @ApiOperation("八字-未来运势")
    @PostMapping("/bazi/weilai")
    public Result weilai(@RequestBody WeilaiRequest weilaiRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        BaziBaseRequest baziBaseRequest = new BaziBaseRequest();
        baziBaseRequest.setProFileId(weilaiRequest.getProFileId());
        baziBaseRequest.setYunshiYear(weilaiRequest.getYunshiYear());
        baziBaseRequest.setComputeDaily(weilaiRequest.getComputeDaily());
        baziBaseRequest.setSect(weilaiRequest.getSect());
        baziBaseRequest.setZhen(weilaiRequest.getZhen());
        baziBaseRequest.setProvince(weilaiRequest.getProvince());
        baziBaseRequest.setCity(weilaiRequest.getCity());
        baziBaseRequest.setLanguageType(userInfoVO.getLanguageType());
        return horoscopeService.WeilaiHoroscope(baziBaseRequest);
    }

    /**
     * 工具-老黄历
     * @param laohuangliRequest
     * @return
     */
    @ApiOperation("工具-老黄历")
    @PostMapping("/gongju/laohuangli")
    public Result laohuangli(@RequestBody LaohuangliRequest laohuangliRequest){
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        laohuangliRequest.setLanguageType(userInfoVO.getLanguageType());
        return toolService.LaohuangliHoroscope(laohuangliRequest);
    }


    /**
     * 工具-老黄历可查询日期列表
     * @return
     */
    @ApiOperation("工具-老黄历可查询日期列表")
    @GetMapping("/gongju/laohuangli/list")
    public Result laohuangliList(){
        return toolService.LaohuangliList();
    }

    /**
     * 工具-择吉日
     * @param zeshiRequest
     * @return
     */
    @ApiOperation("工具-择吉日")
    @PostMapping("/gongju/zeshi")
    public Result zeshi(@RequestBody ZeshiRequest zeshiRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return toolService.ZeshiHoroscope(zeshiRequest,userInfoVO.getLanguageType());
    }
}
