package com.lhx.birthday.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.enums.Relationship;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.request.profile.*;
import com.lhx.birthday.response.profile.*;
import com.lhx.birthday.service.*;
import com.lhx.birthday.util.CommonErrorCode;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.util.converUtil.ConverChinese;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.profile.RegionVO;
import com.lhx.birthday.vo.profile.ProfileVO;
import com.lhx.birthday.vo.profile.RelationshipVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;

import static com.lhx.birthday.constant.SettingConstant.*;

@RequestMapping("/profile")
@RestController
@Slf4j
@Api(description = "app接口 - 人员档案模块", tags = "ProfileController")
public class ProfileController {

    @Autowired
    private IProfileService profileService;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private CommonUtil commonUtil;

    @Autowired
    private RedisService redisService;

    /**
     * 添加人员档案
     * @param profileAddRequest
     * @return
     */
    @ApiOperation("添加人员档案")
    @PostMapping("/add")
    public Result add(@RequestBody ProfileAddRequest profileAddRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        // 监测emoji
        if(containsEmoji(profileAddRequest.getName())){
            return Result.error("真实姓名输入不合法");
        }
        ProfileBaseRequest profiletBaseRequest = new ProfileBaseRequest();
        profiletBaseRequest.setUserId(Long.parseLong(customerId));
        List<ProfileVO> profileList = profileService.getProfileList(profiletBaseRequest);
        int profileNum = profileList.size();
        int maxProfileContact = userInfoVO.getVip().equals(IS_VIP) ? VIP_MAX_PROFILE_CONTACT : USER_MAX_PROFILE_CONTACT;
        if(profileNum>=maxProfileContact){
            return Result.error("超过最大档案数量上限");
        }
        // 判断关系为自己的数据
        boolean hasSelfReference = profileList.stream()
                .anyMatch(profile -> profile.getRelationship().toValue() == 0);
        if (hasSelfReference && profileAddRequest.getRelationship().toValue() == 0) {
            return Result.error("关系选择有误");
        }
        ProfileBaseRequest profileBaseRequest = new ProfileBaseRequest();
        BeanUtils.copyProperties(profileAddRequest, profileBaseRequest);
        profileBaseRequest.setUserId(Long.parseLong(customerId));
        profileBaseRequest.setRegistrationId(userInfoVO.getRegistrationId());
        profileService.addProfile(profileBaseRequest);
        return Result.OK();
    }

    /**
     * 更新人员档案
     * @param profileUpdateRequest
     * @return
     */
    @ApiOperation("更新人员档案")
    @PostMapping("/update")
    public Result update(@RequestBody ProfileUpdateRequest profileUpdateRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        // 监测emoji
        if(containsEmoji(profileUpdateRequest.getName())){
            return Result.error("真实姓名输入不合法");
        }
        ProfileBaseRequest profiletBaseRequest = new ProfileBaseRequest();
        profiletBaseRequest.setUserId(Long.parseLong(customerId));

        Optional<ProfileVO> optionalSelfProfile = profileService.getProfileList(profiletBaseRequest)
                .stream()
                .filter(profileVO -> profileVO.getRelationship().equals(Relationship.SELF))
                .findFirst();

        if (optionalSelfProfile.isPresent()) {
            ProfileVO selfProfile = optionalSelfProfile.get();
            if (!selfProfile.getId().equals(profileUpdateRequest.getId()) &&
                    profileUpdateRequest.getRelationship().equals(Relationship.SELF)) {
                return Result.error(CommonErrorCode.INVALID_PROFILE_RELATIONSHIP, "关系选择有误");
            }
        }

        ProfileBaseRequest profileBaseRequest = new ProfileBaseRequest();
        BeanUtils.copyProperties(profileUpdateRequest, profileBaseRequest);
        profileBaseRequest.setUserId(Long.parseLong(customerId));
        profileBaseRequest.setRegistrationId(userInfoVO.getRegistrationId());
        profileService.updateProfile(profileBaseRequest);
        return Result.OK();
    }

    /**
     * 更新排序
     * @param profileUpdateSortRequest
     * @return
     */
    @ApiOperation("更新排序")
    @PostMapping("/updateSort")
    public Result updateSort(@RequestBody ProfileUpdateSortRequest profileUpdateSortRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        profileService.updateSort(profileUpdateSortRequest);
        return Result.OK();
    }


    /**
     * 删除人员档案
     * @param profileDelRequest
     * @return
     */
    @ApiOperation("删除人员档案")
    @PostMapping("/del")
    public Result del(@RequestBody ProfileDelRequest profileDelRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        ProfileBaseRequest profileBaseRequest = new ProfileBaseRequest();
        profileBaseRequest.setId(profileDelRequest.getId());
        profileBaseRequest.setRegistrationId(userInfoVO.getRegistrationId());
        profileService.delById(profileBaseRequest);
        return Result.OK();
    }

    /**
     * 人员档案列表
     * @return
     */
    @ApiOperation("人员档案列表")
    @RequestMapping(value = "/getList", method = {RequestMethod.GET,RequestMethod.POST})
    public Result<ProfileResponse> getList(@RequestBody ProfileListRequest profileListRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        ProfileBaseRequest profileBaseRequest = new ProfileBaseRequest();
        BeanUtils.copyProperties(profileListRequest, profileBaseRequest);
        profileBaseRequest.setUserId(Long.parseLong(customerId));
        profileBaseRequest.setRelationship(Relationship.OTHER);
        List<ProfileVO> profileList = profileService.getProfileList(profileBaseRequest);

        return Result.OK(ProfileResponse.builder()
                .profiles(profileList)
                .build());
    }

    /**
     * 人员档案详情
     * @param profileDetailRequest
     * @return
     */
    @ApiOperation("人员档案详情")
    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public Result<ProfileDetailResponse> detail(@RequestBody ProfileDetailRequest profileDetailRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        ProfileBaseRequest profileBaseRequest = new ProfileBaseRequest();
        BeanUtils.copyProperties(profileDetailRequest, profileBaseRequest);
        profileBaseRequest.setUserId(Long.parseLong(customerId));
        List<ProfileVO> profileList = profileService.getProfileList(profileBaseRequest);
        ProfileVO profileVO = null;
        if(!profileList.isEmpty()){
            profileVO = profileList.get(0);
            
            // 如果用户语言类型是繁体中文，需要进行转换
            if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
                
                if(profileVO.getName() != null) {
                    profileVO.setName(zhConvertBootstrap.toTraditional(profileVO.getName()));
                }
                if(profileVO.getZodiacSignTypeStr() != null) {
                    profileVO.setZodiacSignTypeStr(zhConvertBootstrap.toTraditional(profileVO.getZodiacSignTypeStr()));
                }
                if(profileVO.getZodiacTypeStr() != null) {
                    profileVO.setZodiacTypeStr(zhConvertBootstrap.toTraditional(profileVO.getZodiacTypeStr()));
                }
                if(profileVO.getRelationshipStr() != null) {
                    profileVO.setRelationshipStr(zhConvertBootstrap.toTraditional(profileVO.getRelationshipStr()));
                }
                if(profileVO.getRemarkInfo() != null) {
                    profileVO.setRemarkInfo(zhConvertBootstrap.toTraditional(profileVO.getRemarkInfo()));
                }
                if(profileVO.getSolarStr() != null) {
                    profileVO.setSolarStr(zhConvertBootstrap.toTraditional(profileVO.getSolarStr()));
                }
                if(profileVO.getSolarYearStr() != null) {
                    profileVO.setSolarYearStr(zhConvertBootstrap.toTraditional(profileVO.getSolarYearStr()));
                }
                if(profileVO.getSolarFullStr() != null) {
                    profileVO.setSolarFullStr(zhConvertBootstrap.toTraditional(profileVO.getSolarFullStr()));
                }
                if(profileVO.getLunarStr() != null) {
                    profileVO.setLunarStr(zhConvertBootstrap.toTraditional(profileVO.getLunarStr()));
                }
                if(profileVO.getLunarYearStr() != null) {
                    profileVO.setLunarYearStr(zhConvertBootstrap.toTraditional(profileVO.getLunarYearStr()));
                }
                if(profileVO.getLunarFullStr() != null) {
                    profileVO.setLunarFullStr(zhConvertBootstrap.toTraditional(profileVO.getLunarFullStr()));
                }
                if(profileVO.getBirthdayInfo() != null) {
                    profileVO.setBirthdayInfo(ConverChinese.convertValuesToTraditionalChinese(profileVO.getBirthdayInfo().toJSONString()));
                }
            }
        }

        return Result.OK(ProfileDetailResponse.builder()
                .profile(profileVO)
                .build());
    }

    /**
     * 人员档案分组列表
     * @return
     */
    @ApiOperation("人员档案分组列表")
    @RequestMapping(value = "/getGroupList", method = {RequestMethod.GET})
    public Result<ProfileGroupResponse> getGroupList(){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        ProfileBaseRequest profileBaseRequest = new ProfileBaseRequest();
        profileBaseRequest.setUserId(Long.parseLong(customerId));
        return Result.OK(profileService.getGroupList(profileBaseRequest));
    }

    /**
     * 关系列表
     * @return
     */
    @ApiOperation("关系列表")
    @GetMapping("/getRelationship")
    public Result<RelationshipResponse> getRelationship(){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        ProfileBaseRequest profiletBaseRequest = new ProfileBaseRequest();
        profiletBaseRequest.setUserId(Long.parseLong(customerId));
        List<ProfileVO> profileList = profileService.getProfileList(profiletBaseRequest);
        boolean hasSelfReference = profileList.stream()
                    .anyMatch(profile -> profile.getRelationship().toValue() == 0);

        List<RelationshipVO> relationshipVOList = new ArrayList<RelationshipVO>();
        for (Relationship value : Relationship.values()) {
            RelationshipVO relationship = new RelationshipVO();
            relationship.setValue(value.toValue());
            String name = value.getValue();
            if(userInfoVO.getLanguageType().equals(LanguageType.EN)){
                name = value.getEnValue();
            }
            if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
                name = zhConvertBootstrap.toTraditional(name);
            }
            relationship.setName(name);
            relationship.setIsSelect(DefaultFlag.YES);
            if (hasSelfReference && value.toValue()==0) {
                relationship.setIsSelect(DefaultFlag.NO);
            }
            relationshipVOList.add(relationship);
        }


        return Result.OK(RelationshipResponse.builder()
                .relationshipS(relationshipVOList)
                .build());
    }

    /**
     * 检查给定的字符串是否包含 Emoji。
     *
     * @param text 要检查的字符串
     * @return 如果字符串中包含 Emoji，则返回 true；否则返回 false
     */
    public static boolean containsEmoji(String text) {
        // 正则表达式匹配 Emoji
        String emojiRegex = "[\\ud83c[\\udf00-\\udfff]|\\ud83d[\\udc00-\\ude4f\\ude80-\\udeff]|\\u2694|\\u2600-\\u26FF\\u2702-\\u27B0]";
        Pattern pattern = Pattern.compile(emojiRegex);

        // 使用正则表达式查找 Emoji
        return pattern.matcher(text).find();
    }
}
