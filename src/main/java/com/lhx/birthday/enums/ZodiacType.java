package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * 生肖类型 0鼠 1牛 2虎 3兔 4龙 5蛇 6马 7羊 8猴 9鸡 10狗 11猪
 * <AUTHOR>
 */
@ApiEnum
public enum ZodiacType {

    @ApiEnumProperty("鼠")
    RAT("鼠", "Rat"),

    @ApiEnumProperty("牛")
    OX("牛", "Ox"),

    @ApiEnumProperty("虎")
    TIGER("虎", "Tiger"),

    @ApiEnumProperty("兔")
    RABBIT("兔", "Rabbit"),

    @ApiEnumProperty("龙")
    DRAGON("龙", "Dragon"),

    @ApiEnumProperty("蛇")
    SNAKE("蛇", "Snake"),

    @ApiEnumProperty("马")
    HORSE("马", "Horse"),

    @ApiEnumProperty("羊")
    GOAT("羊", "Goat"),

    @ApiEnumProperty("猴")
    MONKEY("猴", "Monkey"),

    @ApiEnumProperty("鸡")
    ROOSTER("鸡", "Rooster"),

    @ApiEnumProperty("狗")
    DOG("狗", "Dog"),

    @ApiEnumProperty("猪")
    PIG("猪", "Pig");

    private static final ZodiacType[] VALUES = values();

    private String value;
    private String enValue;

    ZodiacType(String value, String enValue) {
        this.value = value;
        this.enValue = enValue;
    }

    public String getValue() {
        return value;
    }

    public String getEnValue() {
        return enValue;
    }

    public static ZodiacType fromValue(int value) {
        if (value < 0 || value >= VALUES.length) {
            throw new IllegalArgumentException("Invalid value for ZodiacType: " + value);
        }
        return VALUES[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
