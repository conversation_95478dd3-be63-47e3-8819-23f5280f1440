package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * 性别类型 0:自己 1:恋人 2:朋友 3:亲友 4:工作 5:客户 6:案例 7:其他
 * <AUTHOR>
 */
@ApiEnum
public enum Relationship {

    @ApiEnumProperty("0:自己")
    SELF("自己", "Self"),

//    @ApiEnumProperty("1:恋人")
//    LOVER("恋人"),
//
//    @ApiEnumProperty("2:朋友")
//    FRIEND("朋友"),
//
//    @ApiEnumProperty("3:亲友")
//    CLOSEFRIEND("亲友"),
//
//    @ApiEnumProperty("4:工作")
//    WORK("工作"),
//
//    @ApiEnumProperty("5:客户")
//    CUSTOMER("客户"),
//
//    @ApiEnumProperty("6:案例")
//    CASE("案例"),

    @ApiEnumProperty("7:其他")
    OTHER("其他", "Other");

    private String value;
    private String enValue;

    Relationship(String value, String enValue) {
        this.value = value;
        this.enValue = enValue;
    }

    public String getValue() {
        return value;
    }

    public String getEnValue() {
        return enValue;
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
