package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * 星座类型 0白羊座 1金牛座 2双子座 3巨蟹座 4狮子座 5处女座 6天秤座 7天蝎座 8射手座 9摩羯座 10水瓶座 11双鱼座
 * <AUTHOR>
 */
@ApiEnum
public enum ZodiacSignType {

    @ApiEnumProperty("白羊座")
    ARIES("白羊座", "Aries"),

    @ApiEnumProperty("金牛座")
    TAURUS("金牛座", "Taurus"),

    @ApiEnumProperty("双子座")
    GEMINI("双子座", "Gemini"),

    @ApiEnumProperty("巨蟹座")
    CANCER("巨蟹座", "Cancer"),

    @ApiEnumProperty("狮子座")
    LEO("狮子座", "<PERSON>"),

    @ApiEnumProperty("处女座")
    VIRGO("处女座", "Virgo"),

    @ApiEnumProperty("天秤座")
    LIBRA("天秤座", "Libra"),

    @ApiEnumProperty("天蝎座")
    SCORPIO("天蝎座", "Scorpio"),

    @ApiEnumProperty("射手座")
    SAGITTARIUS("射手座", "Sagittarius"),

    @ApiEnumProperty("摩羯座")
    CAPRICORN("摩羯座", "Capricorn"),

    @ApiEnumProperty("水瓶座")
    AQUARIUS("水瓶座", "Aquarius"),

    @ApiEnumProperty("双鱼座")
    PISCES("双鱼座", "Pisces");

    private static final ZodiacSignType[] VALUES = values();

    private String value;
    private String enValue;

    ZodiacSignType(String value, String enValue) {
        this.value = value;
        this.enValue = enValue;
    }

    public String getValue() {
        return value;
    }

    public String getEnValue() {
        return enValue;
    }

    public static ZodiacSignType fromValue(int value) {
        if (value < 0 || value >= VALUES.length) {
            throw new IllegalArgumentException("Invalid value for ZodiacSignType: " + value);
        }
        return VALUES[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}