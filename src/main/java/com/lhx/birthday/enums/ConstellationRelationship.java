package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum ConstellationRelationship {
    SELF("自己", "Self"),
    LOVER("恋人", "Lover"),
    FRIEND("朋友", "Friend"),
    FAMILY("亲友", "Family"),
    WORK("工作", "Work"),
    CLIENT("客户", "Client"),
    CASE("案例", "Case"),
    OTHER("其他", "Other");

    private String value;
    private String enValue;

    ConstellationRelationship(String value, String enValue) {
        this.value = value;
        this.enValue = enValue;
    }

    public String getValue() {
        return value;
    }

    public String getEnValue() {
        return enValue;
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
} 