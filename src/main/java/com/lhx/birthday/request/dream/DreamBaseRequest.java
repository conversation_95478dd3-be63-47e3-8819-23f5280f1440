package com.lhx.birthday.request.dream;

import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "周公解梦请求参数")
public class DreamBaseRequest extends BaseSearchRequest {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "热门")
    private Integer isShow;

    @ApiModelProperty(value = "搜索关键字")
    private String keyword;

    @ApiModelProperty(value = "用户语言类型")
    private LanguageType languageType;


}
