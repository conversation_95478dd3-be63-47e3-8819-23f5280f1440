package com.lhx.birthday.request.subscribe;

import com.lhx.birthday.enums.LanguageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:17
 */
@Getter
@Setter
@ApiModel(description = "订阅请求参数")
public class SubscribeListRequest {


    @ApiModelProperty(value = "客户端 0: ios 1:android")
    private Integer device;

    @ApiModelProperty(value = "用户中心 0:否 1:是")
    private Integer userCenter;

    @ApiModelProperty(value = "语言类型 0简体中文 1繁体中文 2英语")
    private LanguageType languageType;

}
