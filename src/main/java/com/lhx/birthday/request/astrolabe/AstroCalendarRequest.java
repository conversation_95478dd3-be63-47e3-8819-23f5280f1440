package com.lhx.birthday.request.astrolabe;

import com.lhx.birthday.enums.LanguageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "星相日历请求")
public class AstroCalendarRequest {

    @ApiModelProperty(value = "年月，格式 yyyy-MM", example = "2023-05")
    private String yearMonth;

    @ApiModelProperty(value = "具体日期，格式 yyyy-MM-dd，优先级高于yearMonth和dateRange", example = "2023-05-15")
    private String date;

    @ApiModelProperty(value = "语言类型 0简体中文 1繁体中文 2英语")
    private LanguageType languageType;

  @ApiModelProperty(value = "时区，默认8", example = "8")
  private Integer timeZone;
}