package com.lhx.birthday.request.userinfo;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:17
 */
@Getter
@Setter
@ApiModel(description = "游客登录请求参数")
public class TouristsLoginRequest {

    @ApiModelProperty(value = "uuid")
    private String uuid;

    private String osType;

    @ApiModelProperty(value = "推送token")
    private String deviceToken;

    @ApiModelProperty(value = "推送id")
    private String registrationId;

    @ApiModelProperty(value = "时区，默认8")
    @JsonAlias("time_zone")
    private Integer timeZone;

}
