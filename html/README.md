# 星盘AI分析 - H5实时渲染页面

## 功能描述

这是一个基于SSE（Server-Sent Events）的星盘AI分析实时渲染页面，具有以下特性：

- 🌟 **实时数据流渲染**：通过SSE技术实现实时数据接收和渲染
- ⌨️ **打字机效果**：每个字符都有打字机般的实时显示效果
- 📝 **Markdown渲染**：支持标题、粗体、斜体、列表等Markdown格式
- 🎨 **现代化UI**：美观的渐变背景和响应式设计
- 📱 **移动端适配**：支持手机和平板设备
- ⚙️ **参数配置**：可自定义星座ID、盘类型、分析类型等参数

## 使用方法

### 1. 启动后端服务

确保后端服务在 `http://localhost:8082` 运行，并且包含以下接口：
- `POST /birthday/astrolabe/chart/analysis` - 星盘AI分析接口

### 2. 选择页面

- **主要功能页面**: `html/index.html` - 完整的H5页面，包含打字机效果和Markdown渲染
- **实时渲染测试**: `html/realtime.html` - 专门用于测试实时渲染效果（推荐）
- **调试页面**: `html/debug.html` - 专门用于调试SSE数据流问题
- **简单测试页面**: `html/test.html` - 基础功能测试

### 3. 打开H5页面

在浏览器中打开相应的HTML文件。

### 4. 配置参数

- **星座ID**：输入要分析的星座档案ID（默认35）
- **盘类型**：选择星盘类型
  - 1: 本命盘
  - 2: 行运盘（默认）
  - 3: 组合盘
  - 4: 三限盘
  - 5: 次限盘
- **分析类型**：选择分析内容
  - 0: 行星（默认）
  - 1: 宫位
  - 2: 相位

### 5. 开始分析

点击"🚀 开始分析"按钮，页面将：
1. 连接到后端SSE接口
2. 实时接收AI分析结果
3. 以打字机效果显示内容
4. 自动渲染Markdown格式

### 6. 控制功能

- **停止分析**：点击"⏹️ 停止分析"按钮中断连接
- **清空内容**：点击"🗑️ 清空内容"按钮清除显示内容
- **调试信息**：点击"🐛 调试信息"按钮查看原始数据流（仅主页面）

## 技术实现

### 前端技术
- **HTML5**：页面结构
- **CSS3**：样式和动画效果
- **JavaScript ES6+**：交互逻辑和SSE处理
- **Fetch API**：HTTP请求处理
- **ReadableStream**：流数据处理

### 后端技术
- **Spring Boot**：后端框架
- **SSE (Server-Sent Events)**：实时数据推送
- **SseEmitter**：SSE发射器
- **Redis**：缓存分析结果

### 核心功能

#### 1. SSE连接处理
```javascript
fetch(url, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TOKEN}`,
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
    },
    body: JSON.stringify(requestData)
})
```

#### 2. 流数据处理
```javascript
const reader = response.body.getReader();
const decoder = new TextDecoder();

function readStream() {
    return reader.read().then(({ done, value }) => {
        if (done) return;
        
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        
        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data.trim()) {
                    processData(data);
                }
            }
        }
        
        return readStream();
    });
}
```

#### 3. 打字机效果
```javascript
function typeWriter() {
    if (currentIndex < targetContent.length) {
        contentEl.innerHTML = targetContent.substring(0, currentIndex + 1) + '<span class="cursor"></span>';
        currentIndex++;
        typingTimer = setTimeout(typeWriter, typingSpeed);
    } else {
        contentEl.innerHTML = targetContent;
    }
}
```

#### 4. Markdown渲染
```javascript
let renderedContent = content
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/^\* (.*$)/gim, '<li>$1</li>');
```

## 配置说明

### API配置
在 `index.html` 文件中可以修改以下配置：

```javascript
const API_BASE_URL = 'http://localhost:8082';
const TOKEN = 'your-jwt-token-here';
```

### 请求参数
默认请求参数结构：
```javascript
{
    chartType: 2,                    // 盘类型
    planets: [0,1,2,3,4,5,6,7,8,9,"m","H"],  // 行星列表
    planet_xs: [],                   // 小行星
    virtual: [10,"pFortune"],        // 虚星
    h_sys: "p",                      // 宫位系统
    svgType: "1",                    // SVG类型
    constellationId: 35,             // 星座ID
    type: 0                          // 分析类型
}
```

## 注意事项

1. **CORS配置**：确保后端已正确配置CORS以支持跨域请求
2. **Token有效期**：JWT Token需要有效且具有相应权限
3. **网络连接**：需要稳定的网络连接以支持SSE长连接
4. **浏览器兼容性**：建议使用现代浏览器（Chrome、Firefox、Safari、Edge）

## 故障排除

### 常见问题

1. **连接失败**
   - 检查后端服务是否正常运行
   - 确认API地址和端口是否正确
   - 检查Token是否有效

2. **数据不显示或只显示符号**
   - 使用调试页面 `debug.html` 查看原始数据流
   - 检查浏览器控制台是否有错误信息
   - 确认星座ID是否存在
   - 检查网络连接状态

3. **样式异常**
   - 确认CSS文件是否正确加载
   - 检查浏览器是否支持CSS3特性

### 调试方法

1. **使用调试页面**
   - 打开 `debug.html` 页面
   - 查看"原始数据流"区域的数据格式
   - 检查"处理后的内容"是否正确
   - 导出数据进行分析

2. **浏览器开发者工具**
   - 打开浏览器开发者工具
   - 查看Console标签页的错误信息
   - 查看Network标签页的请求状态
   - 检查Application标签页的存储状态

3. **数据格式问题**
   - 如果只显示符号，可能是编码问题
   - 检查后端返回的数据格式
   - 确认SSE数据流的格式是否正确

## 更新日志

### v1.0.0
- 初始版本发布
- 支持SSE实时数据流
- 实现打字机效果
- 支持Markdown渲染
- 响应式设计
