<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单打字机效果</title>
    
    <!-- 引入第三方库 -->
    <script src="https://cdn.jsdelivr.net/npm/typed.js@2.0.16/dist/typed.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .control-group {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .form-group input, .form-group select {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .status {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: 600;
            display: none;
        }

        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            display: block;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            display: block;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            display: block;
        }

        .content {
            padding: 30px;
            min-height: 400px;
        }

        .markdown-content {
            line-height: 1.8;
            font-size: 1.1rem;
            color: #2c3e50;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .markdown-content h1, .markdown-content h2, .markdown-content h3 {
            color: #667eea;
            margin: 20px 0 10px 0;
            font-weight: 600;
        }

        .markdown-content h1 {
            font-size: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .markdown-content h2 {
            font-size: 1.5rem;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 5px;
        }

        .markdown-content h3 {
            font-size: 1.3rem;
        }

        .markdown-content p {
            margin: 15px 0;
        }

        .markdown-content strong {
            color: #667eea;
            font-weight: 700;
        }

        .markdown-content em {
            color: #764ba2;
            font-style: italic;
        }

        .typing-controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .typing-controls label {
            margin-right: 15px;
            font-weight: 600;
        }

        .typing-controls input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }

        .typing-controls span {
            font-family: monospace;
            color: #667eea;
        }

        .cursor-blink {
            animation: blink 1s infinite;
            color: #667eea;
            font-weight: bold;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⌨️ 简单打字机效果</h1>
            <p>专门处理缓存数据的打字机效果</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <div class="form-group">
                    <label for="constellationId">星座ID:</label>
                    <input type="number" id="constellationId" value="37" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="chartType">盘类型:</label>
                    <select id="chartType">
                        <option value="1">本命盘</option>
                        <option value="2" selected>行运盘</option>
                        <option value="3">组合盘</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="type">分析类型:</label>
                    <select id="type">
                        <option value="0" selected>行星</option>
                        <option value="1">宫位</option>
                        <option value="2">相位</option>
                    </select>
                </div>
            </div>
            <div class="control-group">
                <button class="btn btn-primary" id="startBtn" onclick="startAnalysis()">
                    🚀 开始分析
                </button>
                <button class="btn btn-secondary" id="stopBtn" onclick="stopAnalysis()" disabled>
                    ⏹️ 停止分析
                </button>
                <button class="btn btn-secondary" onclick="clearContent()">
                    🗑️ 清空内容
                </button>
            </div>
        </div>

        <div id="status" class="status"></div>

        <div class="typing-controls">
            <label>打字速度: <input type="range" id="typingSpeed" min="10" max="200" value="50"></label>
            <span id="speedValue">50ms</span>
        </div>

        <div class="content">
            <div id="markdownContent" class="markdown-content">
                等待开始分析...
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8082';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInppcCI6IkRFRiJ9.eNpkyzsOwjAMANC7eM5gx2kt5wbsXCAOjqiAtOpHQkLcne7MT-8D22GQgQYkGlRURggwlR0yCScSIo0Bjs3Xyw1yJNQUYLnP3f_W7utr6uV5nR_eTy3uSuYVTY1rQxat3BonNy844ln8vUBmpogoqvH7AwAA__8.DNV8GQX7jSiaL6Eoge5IeucKu9UnTlYKBycowzpVeuE';
        
        let currentContent = '';
        let isConnected = false;
        let typedInstance = null;
        let isStreamingMode = false; // 标记是否为流模式
        let streamUpdateTimer = null; // 流更新定时器

        // 配置Marked.js
        marked.setOptions({
            breaks: true,
            gfm: true
        });

        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function updateSpeedValue() {
            const speed = document.getElementById('typingSpeed').value;
            document.getElementById('speedValue').textContent = speed + 'ms';
        }

        function clearContent() {
            currentContent = '';
            isStreamingMode = false;
            if (streamUpdateTimer) {
                clearTimeout(streamUpdateTimer);
                streamUpdateTimer = null;
            }
            if (typedInstance) {
                typedInstance.destroy();
                typedInstance = null;
            }
            document.getElementById('markdownContent').innerHTML = '等待开始分析...';
        }

        function updateStreamContent(content) {
            // 实时更新流数据显示，不使用打字机效果
            try {
                const htmlContent = marked.parse(content);
                document.getElementById('markdownContent').innerHTML = htmlContent + '<span class="cursor-blink">|</span>';
            } catch (error) {
                console.error('渲染Markdown时出错:', error);
                // 如果Markdown渲染失败，直接显示原始内容
                document.getElementById('markdownContent').innerHTML = content.replace(/\n/g, '<br>') + '<span class="cursor-blink">|</span>';
            }
        }

        function startTypingEffect(content) {
            console.log('启动打字机效果，内容长度:', content.length, '前50个字符:', content.substring(0, 50));

            if (typedInstance) {
                typedInstance.destroy();
                typedInstance = null;
            }

            // 清空内容区域并准备打字机容器
            document.getElementById('markdownContent').innerHTML = '<span id="typing-text"></span>';

            const speed = parseInt(document.getElementById('typingSpeed').value);

            // 先渲染Markdown为HTML
            let htmlContent;
            try {
                htmlContent = marked.parse(content);
                console.log('Markdown渲染成功，HTML长度:', htmlContent.length);
            } catch (error) {
                console.error('Markdown渲染失败:', error);
                htmlContent = content.replace(/\n/g, '<br>');
            }

            typedInstance = new Typed('#typing-text', {
                strings: [htmlContent],
                typeSpeed: speed,
                showCursor: true,
                cursorChar: '|',
                contentType: 'html', // 明确指定内容类型为HTML
                onComplete: function() {
                    console.log('打字机效果完成，内容长度:', content.length);
                    // 打字机完成后，确保内容正确显示
                    document.getElementById('typing-text').innerHTML = htmlContent;
                }
            });
        }

        function processData(data) {
    try {
        console.log('收到数据:', data); // 调试输出

        // 如果是错误消息
        if (data.includes('error')) {
            updateStatus('分析出错: ' + data, 'error');
            return;
        }

        // 检查是否是JSON格式的完整内容（缓存情况）
        if (data.startsWith('{') && data.includes('"content"')) {
            try {
                const jsonData = JSON.parse(data);
                if (jsonData.content) {
                    console.log('收到缓存数据，内容长度:', jsonData.content.length);
                    currentContent = jsonData.content;
                    isStreamingMode = false; // 缓存模式

                    // 对于缓存数据，直接启动打字机效果
                    startTypingEffect(currentContent);
                    updateStatus('开始打字机效果（缓存数据）', 'connected');
                    return;
                }
            } catch (e) {
                console.log('不是有效的JSON数据，按普通文本处理');
            }
        }

        // 处理实时流数据（增量内容）
        if (data && data.trim()) {
            currentContent += data; // 添加新内容到当前内容
            console.log('当前内容长度:', currentContent.length); // 调试输出

            // 标记为流模式
            if (!isStreamingMode) {
                isStreamingMode = true;
                updateStatus('接收流数据中...', 'connected');
            }

            // 对于流数据，使用防抖机制，避免频繁重启打字机
            if (streamUpdateTimer) {
                clearTimeout(streamUpdateTimer);
            }

            // 实时更新显示内容（不使用打字机效果）
            updateStreamContent(currentContent);

            // 设置延迟，如果1000ms内没有新数据，则启动打字机效果
            streamUpdateTimer = setTimeout(() => {
                console.log('流数据接收完成，启动打字机效果，内容长度:', currentContent.length);
                startTypingEffect(currentContent);
                updateStatus('流数据接收完成，开始打字机效果', 'connected');
                isStreamingMode = false;
            }, 1000);
        }

    } catch (error) {
        console.error('处理数据时出错:', error);
    }
}

        function startAnalysis() {
            if (isConnected) {
                stopAnalysis();
            }

            const constellationId = document.getElementById('constellationId').value;
            const chartType = document.getElementById('chartType').value;
            const type = document.getElementById('type').value;

            if (!constellationId) {
                alert('请输入星座ID');
                return;
            }

            const requestData = {
                chartType: parseInt(chartType),
                planets: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, "m", "H"],
                planet_xs: [],
                virtual: [10, "pFortune"],
                h_sys: "p",
                svgType: "1",
                constellationId: parseInt(constellationId),
                type: parseInt(type)
            };

            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateStatus('正在连接服务器...', 'connecting');
            clearContent();

            console.log('发送请求数据:', requestData);

            fetch(`${API_BASE_URL}/birthday/astrolabe/chart/analysis`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${TOKEN}`,
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                updateStatus('连接成功，正在接收数据...', 'connected');
                isConnected = true;
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            updateStatus('分析完成', 'connected');
                            document.getElementById('startBtn').disabled = false;
                            document.getElementById('stopBtn').disabled = true;
                            isConnected = false;

                            // 流结束时，确保启动打字机效果
                            if (isStreamingMode && currentContent.trim()) {
                                console.log('流数据接收完全结束，强制启动打字机效果');
                                if (streamUpdateTimer) {
                                    clearTimeout(streamUpdateTimer);
                                }
                                startTypingEffect(currentContent);
                                isStreamingMode = false;
                            }
                            return;
                        }
                        
                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop();
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data.trim()) {
                                    processData(data);
                                }
                            } else if (line.startsWith('data:')) {
                                // 处理没有空格的情况
                                const data = line.slice(5);
                                if (data.trim()) {
                                    processData(data);
                                }
                            } else if (line.trim() && !line.startsWith(':') && !line.startsWith('event:')) {
                                processData(line);
                            }
                        }
                        
                        return readStream();
                    }).catch(error => {
                        console.error('读取流数据时出错:', error);
                        updateStatus('连接出错: ' + error.message, 'error');
                        document.getElementById('startBtn').disabled = false;
                        document.getElementById('stopBtn').disabled = true;
                        isConnected = false;
                    });
                }
                
                return readStream();
            })
            .catch(error => {
                console.error('请求失败:', error);
                updateStatus('连接失败: ' + error.message, 'error');
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                isConnected = false;
            });
        }

        function stopAnalysis() {
            isConnected = false;
            isStreamingMode = false;
            if (streamUpdateTimer) {
                clearTimeout(streamUpdateTimer);
                streamUpdateTimer = null;
            }
            if (typedInstance) {
                typedInstance.destroy();
                typedInstance = null;
            }
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            updateStatus('分析已停止', '');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('简单打字机效果页面已加载');
            updateSpeedValue();
            
            // 监听速度变化
            document.getElementById('typingSpeed').addEventListener('input', updateSpeedValue);
        });
    </script>
</body>
</html>
