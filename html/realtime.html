<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时渲染测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .control-group {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .form-group input, .form-group select {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .status {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: 600;
            display: none;
        }

        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            display: block;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            display: block;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            display: block;
        }

        .content {
            padding: 30px;
            min-height: 400px;
        }

        .markdown-content {
            line-height: 1.8;
            font-size: 1.1rem;
            color: #2c3e50;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }

        .markdown-content h1, .markdown-content h2, .markdown-content h3 {
            color: #667eea;
            margin: 20px 0 10px 0;
            font-weight: 600;
        }

        .markdown-content h1 {
            font-size: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .markdown-content h2 {
            font-size: 1.5rem;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 5px;
        }

        .markdown-content h3 {
            font-size: 1.3rem;
        }

        .markdown-content p {
            margin: 15px 0;
        }

        .markdown-content strong {
            color: #667eea;
            font-weight: 700;
        }

        .markdown-content em {
            color: #764ba2;
            font-style: italic;
        }

        .cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background: #667eea;
            animation: blink 1s infinite;
            margin-left: 2px;
            vertical-align: text-bottom;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .stats div {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ 实时渲染测试</h1>
            <p>测试SSE数据流的实时渲染效果</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <div class="form-group">
                    <label for="constellationId">星座ID:</label>
                    <input type="number" id="constellationId" value="37" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="chartType">盘类型:</label>
                    <select id="chartType">
                        <option value="1">本命盘</option>
                        <option value="2" selected>行运盘</option>
                        <option value="3">组合盘</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="type">分析类型:</label>
                    <select id="type">
                        <option value="0" selected>行星</option>
                        <option value="1">宫位</option>
                        <option value="2">相位</option>
                    </select>
                </div>
            </div>
            <div class="control-group">
                <button class="btn btn-primary" id="startBtn" onclick="startAnalysis()">
                    🚀 开始测试
                </button>
                <button class="btn btn-secondary" id="stopBtn" onclick="stopAnalysis()" disabled>
                    ⏹️ 停止测试
                </button>
                <button class="btn btn-secondary" onclick="clearContent()">
                    🗑️ 清空内容
                </button>
            </div>
        </div>

        <div id="status" class="status"></div>

        <div class="stats">
            <div>字符数: <span id="charCount">0</span></div>
            <div>数据包: <span id="packetCount">0</span></div>
            <div>连接状态: <span id="connectionStatus">未连接</span></div>
            <div>最后更新: <span id="lastUpdate">-</span></div>
        </div>

        <div class="content">
            <div id="markdownContent" class="markdown-content">
                等待开始测试...
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8082';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInppcCI6IkRFRiJ9.eNpkyzsOwjAMANC7eM5gx2kt5wbsXCAOjqiAtOpHQkLcne7MT-8D22GQgQYkGlRURggwlR0yCScSIo0Bjs3Xyw1yJNQUYLnP3f_W7utr6uV5nR_eTy3uSuYVTY1rQxat3BonNy844ln8vUBmpogoqvH7AwAA__8.DNV8GQX7jSiaL6Eoge5IeucKu9UnTlYKBycowzpVeuE';
        
        let currentContent = '';
        let isConnected = false;
        let charCount = 0;
        let packetCount = 0;

        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function updateStats() {
            document.getElementById('charCount').textContent = charCount;
            document.getElementById('packetCount').textContent = packetCount;
            document.getElementById('connectionStatus').textContent = isConnected ? '已连接' : '未连接';
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        function clearContent() {
            currentContent = '';
            charCount = 0;
            packetCount = 0;
            document.getElementById('markdownContent').innerHTML = '等待开始测试...';
            updateStats();
        }

        function renderMarkdown(content) {
            const contentEl = document.getElementById('markdownContent');
            
            // 简单的markdown渲染
            let renderedContent = content
                // 标题
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                // 粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // 斜体
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                // 列表
                .replace(/^\* (.*$)/gim, '<li>$1</li>')
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                // 段落
                .replace(/\n\n/g, '</p><p>')
                .replace(/^(?!<[h|li])(.*$)/gim, '<p>$1</p>')
                // 清理多余的p标签
                .replace(/<p><\/p>/g, '')
                .replace(/<p><p>/g, '<p>')
                .replace(/<\/p><\/p>/g, '</p>');

            // 直接显示内容
            contentEl.innerHTML = renderedContent + '<span class="cursor"></span>';
        }

        function processData(data) {
            try {
                packetCount++;
                charCount += data.length;
                
                console.log('收到数据包:', packetCount, '数据:', data);
                
                // 如果是错误消息
                if (data.includes('error')) {
                    updateStatus('分析出错: ' + data, 'error');
                    return;
                }

                // 检查是否是JSON格式的完整内容（缓存情况）
                if (data.startsWith('{') && data.includes('"content"')) {
                    try {
                        const jsonData = JSON.parse(data);
                        if (jsonData.content) {
                            console.log('收到缓存数据，内容长度:', jsonData.content.length);
                            currentContent = jsonData.content;
                            charCount = currentContent.length;
                            renderMarkdown(currentContent);
                            updateStats();
                            return;
                        }
                    } catch (e) {
                        console.log('不是有效的JSON数据，按普通文本处理');
                    }
                }

                // 处理实时流数据（增量内容）
                if (data && data.trim()) {
                    // 添加新内容到当前内容
                    currentContent += data;
                    
                    // 实时渲染markdown内容
                    renderMarkdown(currentContent);
                    updateStats();
                }
                
            } catch (error) {
                console.error('处理数据时出错:', error);
            }
        }

        function startAnalysis() {
            if (isConnected) {
                stopAnalysis();
            }

            const constellationId = document.getElementById('constellationId').value;
            const chartType = document.getElementById('chartType').value;
            const type = document.getElementById('type').value;

            if (!constellationId) {
                alert('请输入星座ID');
                return;
            }

            const requestData = {
                chartType: parseInt(chartType),
                planets: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, "m", "H"],
                planet_xs: [],
                virtual: [10, "pFortune"],
                h_sys: "p",
                svgType: "1",
                constellationId: parseInt(constellationId),
                type: parseInt(type)
            };

            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateStatus('正在连接服务器...', 'connecting');
            clearContent();

            console.log('发送请求数据:', requestData);

            fetch(`${API_BASE_URL}/birthday/astrolabe/chart/analysis`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${TOKEN}`,
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                updateStatus('连接成功，正在接收数据...', 'connected');
                isConnected = true;
                updateStats();
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            updateStatus('测试完成', 'connected');
                            document.getElementById('startBtn').disabled = false;
                            document.getElementById('stopBtn').disabled = true;
                            isConnected = false;
                            updateStats();
                            return;
                        }
                        
                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop();
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data.trim()) {
                                    processData(data);
                                }
                            } else if (line.startsWith('data:')) {
                                // 处理没有空格的情况
                                const data = line.slice(5);
                                if (data.trim()) {
                                    processData(data);
                                }
                            } else if (line.trim() && !line.startsWith(':') && !line.startsWith('event:')) {
                                processData(line);
                            }
                        }
                        
                        return readStream();
                    }).catch(error => {
                        console.error('读取流数据时出错:', error);
                        updateStatus('连接出错: ' + error.message, 'error');
                        document.getElementById('startBtn').disabled = false;
                        document.getElementById('stopBtn').disabled = true;
                        isConnected = false;
                        updateStats();
                    });
                }
                
                return readStream();
            })
            .catch(error => {
                console.error('请求失败:', error);
                updateStatus('连接失败: ' + error.message, 'error');
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                isConnected = false;
                updateStats();
            });
        }

        function stopAnalysis() {
            isConnected = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            updateStatus('测试已停止', '');
            updateStats();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('实时渲染测试页面已加载');
            updateStats();
        });
    </script>
</body>
</html>
