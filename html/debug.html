<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .raw-data {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 SSE数据调试页面</h1>
        
        <div class="section">
            <h3>参数配置</h3>
            <div class="controls">
                <label>星座ID: <input type="number" id="constellationId" value="35"></label>
                <label>盘类型: 
                    <select id="chartType">
                        <option value="1">本命盘</option>
                        <option value="2" selected>行运盘</option>
                        <option value="3">组合盘</option>
                    </select>
                </label>
                <label>分析类型: 
                    <select id="type">
                        <option value="0" selected>行星</option>
                        <option value="1">宫位</option>
                        <option value="2">相位</option>
                    </select>
                </label>
            </div>
        </div>
        
        <div class="section">
            <h3>控制按钮</h3>
            <button onclick="startTest()">🚀 开始测试</button>
            <button onclick="stopTest()" id="stopBtn" disabled>⏹️ 停止测试</button>
            <button onclick="clearAll()">🗑️ 清空所有</button>
            <button onclick="exportData()">📤 导出数据</button>
        </div>
        
        <div id="status" class="status"></div>
        
        <div class="section">
            <h3>处理后的内容</h3>
            <div id="processedContent" class="content">等待开始...</div>
        </div>
        
        <div class="section">
            <h3>原始数据流</h3>
            <div id="rawData" class="raw-data">等待数据...</div>
        </div>
        
        <div class="section">
            <h3>数据统计</h3>
            <div id="stats">
                <p>总数据包: <span id="totalPackets">0</span></p>
                <p>总字符数: <span id="totalChars">0</span></p>
                <p>当前内容长度: <span id="contentLength">0</span></p>
                <p>连接状态: <span id="connectionStatus">未连接</span></p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8082';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInppcCI6IkRFRiJ9.eNpkyzsOwjAMANC7eM5gx2kt5wbsXCAOjqiAtOpHQkLcne7MT-8D22GQgQYkGlRURggwlR0yCScSIo0Bjs3Xyw1yJNQUYLnP3f_W7utr6uV5nR_eTy3uSuYVTY1rQxat3BonNy844ln8vUBmpogoqvH7AwAA__8.DNV8GQX7jSiaL6Eoge5IeucKu9UnTlYKBycowzpVeuE';
        
        let currentContent = '';
        let isConnected = false;
        let totalPackets = 0;
        let totalChars = 0;
        let rawDataLog = [];
        let processedDataLog = [];

        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function updateStats() {
            document.getElementById('totalPackets').textContent = totalPackets;
            document.getElementById('totalChars').textContent = totalChars;
            document.getElementById('contentLength').textContent = currentContent.length;
            document.getElementById('connectionStatus').textContent = isConnected ? '已连接' : '未连接';
        }

        function updateRawData() {
            const rawDataEl = document.getElementById('rawData');
            rawDataEl.textContent = rawDataLog.join('\n');
            rawDataEl.scrollTop = rawDataEl.scrollHeight;
        }

        function updateProcessedContent() {
            const contentEl = document.getElementById('processedContent');
            contentEl.textContent = currentContent;
            contentEl.scrollTop = contentEl.scrollHeight;
        }

        function clearAll() {
            currentContent = '';
            totalPackets = 0;
            totalChars = 0;
            rawDataLog = [];
            processedDataLog = [];
            updateStats();
            updateRawData();
            updateProcessedContent();
        }

        function exportData() {
            const data = {
                timestamp: new Date().toISOString(),
                totalPackets,
                totalChars,
                contentLength: currentContent.length,
                rawData: rawDataLog,
                processedContent: currentContent
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sse-debug-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function processData(data) {
            try {
                totalPackets++;
                totalChars += data.length;
                
                // 记录原始数据
                rawDataLog.push(`[${new Date().toLocaleTimeString()}] ${data}`);
                if (rawDataLog.length > 100) {
                    rawDataLog.shift();
                }
                
                console.log('收到数据包:', totalPackets, '数据:', data);
                
                // 如果是错误消息
                if (data.includes('error')) {
                    updateStatus('分析出错: ' + data, 'error');
                    return;
                }

                // 检查是否是JSON格式的完整内容（缓存情况）
                if (data.startsWith('{') && data.includes('"content"')) {
                    try {
                        const jsonData = JSON.parse(data);
                        if (jsonData.content) {
                            console.log('收到缓存数据，内容长度:', jsonData.content.length);
                            currentContent = jsonData.content;
                            processedDataLog.push(`[缓存数据] 长度: ${jsonData.content.length}`);
                            updateStats();
                            updateProcessedContent();
                            updateRawData();
                            return;
                        }
                    } catch (e) {
                        console.log('不是有效的JSON数据，按普通文本处理');
                    }
                }

                // 处理实时流数据（增量内容）
                if (data && data.trim()) {
                    // 添加新内容到当前内容
                    currentContent += data;
                    processedDataLog.push(`[增量数据] 长度: ${data.length}, 累计: ${currentContent.length}`);
                    
                    updateStats();
                    updateProcessedContent();
                    updateRawData();
                }
                
            } catch (error) {
                console.error('处理数据时出错:', error);
                updateStatus('处理数据出错: ' + error.message, 'error');
            }
        }

        function startTest() {
            if (isConnected) {
                stopTest();
            }

            const constellationId = document.getElementById('constellationId').value;
            const chartType = document.getElementById('chartType').value;
            const type = document.getElementById('type').value;

            if (!constellationId) {
                alert('请输入星座ID');
                return;
            }

            const requestData = {
                chartType: parseInt(chartType),
                planets: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, "m", "H"],
                planet_xs: [],
                virtual: [10, "pFortune"],
                h_sys: "p",
                svgType: "1",
                constellationId: parseInt(constellationId),
                type: parseInt(type)
            };

            document.getElementById('stopBtn').disabled = false;
            updateStatus('正在连接服务器...', 'info');
            clearAll();

            console.log('发送请求数据:', requestData);

            fetch(`${API_BASE_URL}/birthday/astrolabe/chart/analysis`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${TOKEN}`,
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                updateStatus('连接成功，正在接收数据...', 'success');
                isConnected = true;
                updateStats();
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            updateStatus('测试完成', 'success');
                            document.getElementById('stopBtn').disabled = true;
                            isConnected = false;
                            updateStats();
                            return;
                        }
                        
                        const chunk = decoder.decode(value, { stream: true });
                        console.log('收到原始数据块:', chunk);
                        
                        buffer += chunk;
                        const lines = buffer.split('\n');
                        buffer = lines.pop();
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data.trim()) {
                                    processData(data);
                                }
                            } else if (line.trim() && !line.startsWith(':') && !line.startsWith('event:')) {
                                processData(line);
                            }
                        }
                        
                        return readStream();
                    }).catch(error => {
                        console.error('读取流数据时出错:', error);
                        updateStatus('连接出错: ' + error.message, 'error');
                        document.getElementById('stopBtn').disabled = true;
                        isConnected = false;
                        updateStats();
                    });
                }
                
                return readStream();
            })
            .catch(error => {
                console.error('请求失败:', error);
                updateStatus('连接失败: ' + error.message, 'error');
                document.getElementById('stopBtn').disabled = true;
                isConnected = false;
                updateStats();
            });
        }

        function stopTest() {
            isConnected = false;
            document.getElementById('stopBtn').disabled = true;
            updateStatus('测试已停止', 'info');
            updateStats();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SSE调试页面已加载');
            console.log('API地址:', API_BASE_URL);
            console.log('Token:', TOKEN);
            updateStats();
        });
    </script>
</body>
</html>
