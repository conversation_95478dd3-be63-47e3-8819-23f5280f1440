<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 SSE连接测试</h1>
        
        <div>
            <label>星座ID: <input type="number" id="constellationId" value="35"></label>
            <label>盘类型: 
                <select id="chartType">
                    <option value="1">本命盘</option>
                    <option value="2" selected>行运盘</option>
                    <option value="3">组合盘</option>
                </select>
            </label>
            <label>分析类型: 
                <select id="type">
                    <option value="0" selected>行星</option>
                    <option value="1">宫位</option>
                    <option value="2">相位</option>
                </select>
            </label>
        </div>
        
        <div>
            <button onclick="testConnection()">🚀 测试连接</button>
            <button onclick="stopTest()" id="stopBtn" disabled>⏹️ 停止测试</button>
            <button onclick="clearContent()">🗑️ 清空内容</button>
        </div>
        
        <div id="status" class="status"></div>
        <div id="content" class="content">等待测试...</div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8082';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInppcCI6IkRFRiJ9.eNpkyzsOwjAMANC7eM5gx2kt5wbsXCAOjqiAtOpHQkLcne7MT-8D22GQgQYkGlRURggwlR0yCScSIo0Bjs3Xyw1yJNQUYLnP3f_W7utr6uV5nR_eTy3uSuYVTY1rQxat3BonNy844ln8vUBmpogoqvH7AwAA__8.DNV8GQX7jSiaL6Eoge5IeucKu9UnTlYKBycowzpVeuE';
        
        let currentContent = '';
        let isConnected = false;

        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function updateContent(text) {
            currentContent += text;
            document.getElementById('content').textContent = currentContent;
        }

        function clearContent() {
            currentContent = '';
            document.getElementById('content').textContent = '等待测试...';
        }

        function testConnection() {
            if (isConnected) {
                stopTest();
            }

            const constellationId = document.getElementById('constellationId').value;
            const chartType = document.getElementById('chartType').value;
            const type = document.getElementById('type').value;

            if (!constellationId) {
                alert('请输入星座ID');
                return;
            }

            const requestData = {
                chartType: parseInt(chartType),
                planets: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, "m", "H"],
                planet_xs: [],
                virtual: [10, "pFortune"],
                h_sys: "p",
                svgType: "1",
                constellationId: parseInt(constellationId),
                type: parseInt(type)
            };

            document.getElementById('stopBtn').disabled = false;
            updateStatus('正在连接服务器...', 'info');
            clearContent();

            console.log('发送请求数据:', requestData);

            fetch(`${API_BASE_URL}/birthday/astrolabe/chart/analysis`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${TOKEN}`,
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                updateStatus('连接成功，正在接收数据...', 'success');
                isConnected = true;
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            updateStatus('测试完成', 'success');
                            document.getElementById('stopBtn').disabled = true;
                            isConnected = false;
                            return;
                        }
                        
                        const chunk = decoder.decode(value, { stream: true });
                        console.log('收到数据块:', chunk);
                        
                        buffer += chunk;
                        const lines = buffer.split('\n');
                        buffer = lines.pop();
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data.trim()) {
                                    console.log('处理数据:', data);
                                    updateContent(data);
                                }
                            }
                        }
                        
                        return readStream();
                    }).catch(error => {
                        console.error('读取流数据时出错:', error);
                        updateStatus('连接出错: ' + error.message, 'error');
                        document.getElementById('stopBtn').disabled = true;
                        isConnected = false;
                    });
                }
                
                return readStream();
            })
            .catch(error => {
                console.error('请求失败:', error);
                updateStatus('连接失败: ' + error.message, 'error');
                document.getElementById('stopBtn').disabled = true;
                isConnected = false;
            });
        }

        function stopTest() {
            isConnected = false;
            document.getElementById('stopBtn').disabled = true;
            updateStatus('测试已停止', 'info');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SSE测试页面已加载');
            console.log('API地址:', API_BASE_URL);
            console.log('Token:', TOKEN);
        });
    </script>
</body>
</html>
