<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星盘AI分析 - 实时渲染</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .control-group {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .control-group:last-child {
            margin-bottom: 0;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .form-group input, .form-group select {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: 600;
            display: none;
        }

        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            display: block;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            display: block;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            display: block;
        }

        .content {
            padding: 30px;
            min-height: 400px;
        }

        .markdown-content {
            line-height: 1.8;
            font-size: 1.1rem;
            color: #2c3e50;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }

        .markdown-content h1, .markdown-content h2, .markdown-content h3 {
            color: #667eea;
            margin: 20px 0 10px 0;
            font-weight: 600;
        }

        .markdown-content h1 {
            font-size: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .markdown-content h2 {
            font-size: 1.5rem;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 5px;
        }

        .markdown-content h3 {
            font-size: 1.3rem;
        }

        .markdown-content p {
            margin: 15px 0;
        }

        .markdown-content ul, .markdown-content ol {
            margin: 15px 0;
            padding-left: 30px;
        }

        .markdown-content li {
            margin: 8px 0;
        }

        .markdown-content strong {
            color: #667eea;
            font-weight: 700;
        }

        .markdown-content em {
            color: #764ba2;
            font-style: italic;
        }

        .markdown-content blockquote {
            border-left: 4px solid #667eea;
            padding-left: 20px;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 0 10px 10px 0;
        }

        .cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background: #667eea;
            animation: blink 1s infinite;
            margin-left: 2px;
            vertical-align: text-bottom;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #667eea;
            font-size: 1.2rem;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e9ecef;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .controls {
                padding: 20px;
            }

            .control-group {
                flex-direction: column;
                align-items: stretch;
            }

            .content {
                padding: 20px;
            }

            .markdown-content {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 星盘AI分析</h1>
            <p>实时渲染您的星盘分析结果</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <div class="form-group">
                    <label for="constellationId">星座ID:</label>
                    <input type="number" id="constellationId" value="37" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="chartType">盘类型:</label>
                    <select id="chartType">
                        <option value="1">本命盘</option>
                        <option value="2" selected>行运盘</option>
                        <option value="3">组合盘</option>
                        <option value="4">三限盘</option>
                        <option value="5">次限盘</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="type">分析类型:</label>
                    <select id="type">
                        <option value="0" selected>行星</option>
                        <option value="1">宫位</option>
                        <option value="2">相位</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="typingEffect">打字机效果:</label>
                    <select id="typingEffect">
                        <option value="false" selected>关闭（实时显示）</option>
                        <option value="true">开启</option>
                    </select>
                </div>
            </div>
            <div class="control-group">
                <button class="btn btn-primary" id="startBtn" onclick="startAnalysis()">
                    🚀 开始分析
                </button>
                <button class="btn btn-secondary" id="stopBtn" onclick="stopAnalysis()" disabled>
                    ⏹️ 停止分析
                </button>
                <button class="btn btn-secondary" onclick="clearContent()">
                    🗑️ 清空内容
                </button>
                <button class="btn btn-secondary" onclick="toggleDebug()">
                    🐛 调试信息
                </button>
            </div>
        </div>

        <div id="status" class="status"></div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="content">
            <div id="markdownContent" class="markdown-content">
                <div class="loading">
                    <div class="spinner"></div>
                    等待开始分析...
                </div>
            </div>
            
            <!-- 调试信息区域 -->
            <div id="debugInfo" style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 12px; display: none;">
                <h4>调试信息</h4>
                <div id="rawData" style="max-height: 200px; overflow-y: auto; background: white; padding: 10px; border: 1px solid #ddd; margin-top: 10px;"></div>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let isConnected = false;
        let currentContent = '';
        let typingSpeed = 50; // 打字机效果速度（毫秒）
        let typingTimer = null;
        let debugMode = false;
        let rawDataLog = [];

        // 配置
        const API_BASE_URL = 'http://localhost:8082';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInppcCI6IkRFRiJ9.eNpkyzsOwjAMANC7eM5gx2kt5wbsXCAOjqiAtOpHQkLcne7MT-8D22GQgQYkGlRURggwlR0yCScSIo0Bjs3Xyw1yJNQUYLnP3f_W7utr6uV5nR_eTy3uSuYVTY1rQxat3BonNy844ln8vUBmpogoqvH7AwAA__8.DNV8GQX7jSiaL6Eoge5IeucKu9UnTlYKBycowzpVeuE';

        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function updateProgress(percent) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = `${percent}%`;
        }

        function clearContent() {
            currentContent = '';
            document.getElementById('markdownContent').innerHTML = '<div class="loading"><div class="spinner"></div>等待开始分析...</div>';
            updateProgress(0);
            rawDataLog = [];
            updateDebugInfo();
        }

        function toggleDebug() {
            debugMode = !debugMode;
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.style.display = debugMode ? 'block' : 'none';
            if (debugMode) {
                updateDebugInfo();
            }
        }

        function updateDebugInfo() {
            if (!debugMode) return;
            const rawDataEl = document.getElementById('rawData');
            rawDataEl.innerHTML = rawDataLog.map((item, index) => 
                `<div style="margin-bottom: 5px;"><strong>${index + 1}.</strong> ${item}</div>`
            ).join('');
            rawDataEl.scrollTop = rawDataEl.scrollHeight;
        }

        function startAnalysis() {
            if (isConnected) {
                stopAnalysis();
            }

            const constellationId = document.getElementById('constellationId').value;
            const chartType = document.getElementById('chartType').value;
            const type = document.getElementById('type').value;

            if (!constellationId) {
                alert('请输入星座ID');
                return;
            }

            // 准备请求参数
            const requestData = {
                chartType: parseInt(chartType),
                planets: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, "m", "H"],
                planet_xs: [],
                virtual: [10, "pFortune"],
                h_sys: "p",
                svgType: "1",
                constellationId: parseInt(constellationId),
                type: parseInt(type)
            };

            // 更新UI状态
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateStatus('正在连接服务器...', 'connecting');
            updateProgress(10);

            // 清空之前的内容
            currentContent = '';
            document.getElementById('markdownContent').innerHTML = '<div class="loading"><div class="spinner"></div>正在连接...</div>';

            try {
                // 创建EventSource连接
                const url = `${API_BASE_URL}/birthday/astrolabe/chart/analysis`;
                
                // 使用fetch API发送POST请求，因为EventSource只支持GET
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${TOKEN}`,
                        'Accept': 'text/event-stream',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    updateStatus('连接成功，正在接收数据...', 'connected');
                    updateProgress(30);
                    
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';
                    
                    function readStream() {
                        return reader.read().then(({ done, value }) => {
                            if (done) {
                                updateStatus('分析完成', 'connected');
                                updateProgress(100);
                                document.getElementById('startBtn').disabled = false;
                                document.getElementById('stopBtn').disabled = true;
                                return;
                            }
                            
                            buffer += decoder.decode(value, { stream: true });
                            const lines = buffer.split('\n');
                            buffer = lines.pop(); // 保留不完整的行
                            
                            for (const line of lines) {
                                if (line.startsWith('data: ')) {
                                    const data = line.slice(6);
                                    if (data.trim()) {
                                        console.log('处理SSE数据行:', data);
                                        processData(data);
                                    }
                                } else if (line.trim() && !line.startsWith(':') && !line.startsWith('event:')) {
                                    // 处理没有data:前缀的数据
                                    console.log('处理非标准SSE数据:', line);
                                    processData(line);
                                }
                            }
                            
                            return readStream();
                        }).catch(error => {
                            console.error('读取流数据时出错:', error);
                            updateStatus('连接出错: ' + error.message, 'error');
                            document.getElementById('startBtn').disabled = false;
                            document.getElementById('stopBtn').disabled = true;
                        });
                    }
                    
                    return readStream();
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    updateStatus('连接失败: ' + error.message, 'error');
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                });

            } catch (error) {
                console.error('创建连接时出错:', error);
                updateStatus('创建连接失败: ' + error.message, 'error');
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
            }
        }

        function processData(data) {
            try {
                console.log('收到数据:', data);
                
                // 记录调试信息
                rawDataLog.push(`[${new Date().toLocaleTimeString()}] ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`);
                if (rawDataLog.length > 50) {
                    rawDataLog.shift(); // 保持最多50条记录
                }
                updateDebugInfo();
                
                // 如果是错误消息
                if (data.includes('error')) {
                    updateStatus('分析出错: ' + data, 'error');
                    return;
                }

                // 检查是否是JSON格式的完整内容（缓存情况）
                if (data.startsWith('{') && data.includes('"content"')) {
                    try {
                        const jsonData = JSON.parse(data);
                        if (jsonData.content) {
                            console.log('收到缓存数据，内容长度:', jsonData.content.length);
                            currentContent = jsonData.content;
                            updateProgress(100);
                            renderMarkdown(currentContent);
                            return;
                        }
                    } catch (e) {
                        console.log('不是有效的JSON数据，按普通文本处理');
                    }
                }

                // 处理实时流数据（增量内容）
                if (data && data.trim()) {
                    // 添加新内容到当前内容
                    currentContent += data;
                    
                    // 更新进度（基于内容长度估算）
                    const progress = Math.min(30 + (currentContent.length / 1000) * 60, 90);
                    updateProgress(progress);

                    // 根据设置选择渲染方式
                    const useTypingEffect = document.getElementById('typingEffect').value === 'true';
                    if (useTypingEffect) {
                        renderMarkdown(currentContent);
                    } else {
                        renderMarkdownRealTime(currentContent);
                    }
                }
                
            } catch (error) {
                console.error('处理数据时出错:', error);
            }
        }

        function renderMarkdown(content) {
            const contentEl = document.getElementById('markdownContent');
            
            // 简单的markdown渲染
            let renderedContent = content
                // 标题
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                // 粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // 斜体
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                // 列表
                .replace(/^\* (.*$)/gim, '<li>$1</li>')
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                // 段落
                .replace(/\n\n/g, '</p><p>')
                .replace(/^(?!<[h|li])(.*$)/gim, '<p>$1</p>')
                // 清理多余的p标签
                .replace(/<p><\/p>/g, '')
                .replace(/<p><p>/g, '<p>')
                .replace(/<\/p><\/p>/g, '</p>');

            // 添加打字机效果
            if (typingTimer) {
                clearTimeout(typingTimer);
            }

            let currentIndex = 0;
            const targetContent = renderedContent;
            
            function typeWriter() {
                if (currentIndex < targetContent.length) {
                    // 使用substring确保正确处理中文字符
                    const displayContent = targetContent.substring(0, currentIndex + 1);
                    contentEl.innerHTML = displayContent + '<span class="cursor"></span>';
                    currentIndex++;
                    
                    // 调整打字速度，中文字符稍慢一些
                    const char = targetContent.charAt(currentIndex - 1);
                    const speed = /[\u4e00-\u9fa5]/.test(char) ? typingSpeed * 1.5 : typingSpeed;
                    
                    typingTimer = setTimeout(typeWriter, speed);
                } else {
                    contentEl.innerHTML = targetContent;
                }
            }
            
            typeWriter();
        }

        function renderMarkdownRealTime(content) {
            const contentEl = document.getElementById('markdownContent');
            
            // 简单的markdown渲染
            let renderedContent = content
                // 标题
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                // 粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // 斜体
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                // 列表
                .replace(/^\* (.*$)/gim, '<li>$1</li>')
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                // 段落
                .replace(/\n\n/g, '</p><p>')
                .replace(/^(?!<[h|li])(.*$)/gim, '<p>$1</p>')
                // 清理多余的p标签
                .replace(/<p><\/p>/g, '')
                .replace(/<p><p>/g, '<p>')
                .replace(/<\/p><\/p>/g, '</p>');

            // 直接显示内容，不使用打字机效果
            contentEl.innerHTML = renderedContent + '<span class="cursor"></span>';
        }

        function stopAnalysis() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            
            isConnected = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            updateStatus('连接已停止', '');
            updateProgress(0);
            
            if (typingTimer) {
                clearTimeout(typingTimer);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('星盘AI分析页面已加载');
            console.log('API地址:', API_BASE_URL);
            console.log('Token:', TOKEN);
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            stopAnalysis();
        });
    </script>
</body>
</html>
